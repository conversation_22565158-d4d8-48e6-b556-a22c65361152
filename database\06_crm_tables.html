<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>جداول إدارة العملاء (CRM) - نظام ERP للخياطة</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            margin-top: 20px;
            margin-bottom: 20px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            padding: 40px 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 2.5em;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .section {
            margin-bottom: 50px;
            padding: 30px;
            background: #f8f9fa;
            border-radius: 15px;
            border: 1px solid #e9ecef;
        }
        
        .section h2 {
            color: #495057;
            font-size: 2em;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 3px solid #667eea;
        }
        
        .section h3 {
            color: #6c757d;
            font-size: 1.5em;
            margin: 25px 0 15px 0;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border-left: 4px solid #28a745;
        }
        
        .table-container {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin: 20px 0;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            padding: 12px 15px;
            text-align: right;
            border-bottom: 1px solid #dee2e6;
            font-size: 0.9em;
        }
        
        th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: 600;
            position: sticky;
            top: 0;
        }
        
        tr:hover {
            background: #f8f9fa;
        }
        
        .primary-key {
            background: #fff3cd;
            font-weight: bold;
        }
        
        .foreign-key {
            background: #d1ecf1;
        }
        
        .required {
            color: #dc3545;
            font-weight: bold;
        }
        
        .optional {
            color: #6c757d;
        }
        
        .sql-code {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.85em;
            overflow-x: auto;
            margin: 20px 0;
            white-space: pre-wrap;
        }
        
        .sql-keyword {
            color: #63b3ed;
            font-weight: bold;
        }
        
        .sql-string {
            color: #68d391;
        }
        
        .sql-comment {
            color: #a0aec0;
            font-style: italic;
        }
        
        .info-box {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-left: 5px solid #17a2b8;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .nav-links {
            background: #e9ecef;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .nav-links a {
            display: inline-block;
            margin: 5px 10px;
            padding: 10px 20px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-weight: 500;
        }
        
        .nav-links a:hover {
            background: #5a67d8;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>جداول إدارة العملاء (CRM)</h1>
            <p>نظام ERP لمعامل ومحلات الخياطة</p>
        </div>

        <div class="nav-links">
            <a href="../index.html">الصفحة الرئيسية</a>
            <a href="01_core_tables.html">الجداول الأساسية</a>
            <a href="02_product_inventory_tables.html">المنتجات والمخزون</a>
            <a href="03_sales_pos_tables.html">المبيعات ونقطة البيع</a>
            <a href="04_accounting_tables.html">المحاسبة</a>
            <a href="05_manufacturing_tables.html">التصنيع</a>
            <a href="06_crm_tables.html">إدارة العملاء</a>
            <a href="07_purchasing_tables.html">المشتريات والموردين</a>
            <a href="08_system_tables.html">جداول النظام</a>
        </div>

        <div class="section">
            <h2>6. جداول إدارة العملاء (CRM)</h2>
            
            <div class="info-box">
                <h4>نظرة عامة:</h4>
                <p>هذه الجداول تدير علاقات العملاء بشكل شامل من العملاء المحتملين إلى الفرص التجارية والأنشطة والمتابعات.</p>
            </div>

            <h3>6.1 جدول العملاء المحتملين (leads)</h3>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>اسم الحقل</th>
                            <th>نوع البيانات</th>
                            <th>القيود</th>
                            <th>الوصف</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="primary-key">
                            <td>id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>PRIMARY KEY, AUTO_INCREMENT</td>
                            <td>المعرف الفريد للعميل المحتمل</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>company_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NOT NULL, FOREIGN KEY</td>
                            <td>معرف الشركة</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>assigned_to</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NULLABLE, FOREIGN KEY</td>
                            <td>معرف المندوب المسؤول</td>
                        </tr>
                        <tr class="required">
                            <td>lead_code</td>
                            <td>VARCHAR(50)</td>
                            <td>NOT NULL</td>
                            <td>رمز العميل المحتمل</td>
                        </tr>
                        <tr class="required">
                            <td>name</td>
                            <td>VARCHAR(255)</td>
                            <td>NOT NULL</td>
                            <td>اسم العميل المحتمل</td>
                        </tr>
                        <tr class="optional">
                            <td>company_name</td>
                            <td>VARCHAR(255)</td>
                            <td>NULLABLE</td>
                            <td>اسم الشركة</td>
                        </tr>
                        <tr class="optional">
                            <td>phone</td>
                            <td>VARCHAR(20)</td>
                            <td>NULLABLE</td>
                            <td>رقم الهاتف</td>
                        </tr>
                        <tr class="optional">
                            <td>email</td>
                            <td>VARCHAR(255)</td>
                            <td>NULLABLE</td>
                            <td>البريد الإلكتروني</td>
                        </tr>
                        <tr class="optional">
                            <td>address</td>
                            <td>TEXT</td>
                            <td>NULLABLE</td>
                            <td>العنوان</td>
                        </tr>
                        <tr class="required">
                            <td>lead_source</td>
                            <td>ENUM</td>
                            <td>('website','phone','referral','social_media','advertisement','walk_in','other')</td>
                            <td>مصدر العميل المحتمل</td>
                        </tr>
                        <tr class="required">
                            <td>lead_status</td>
                            <td>ENUM</td>
                            <td>('new','contacted','qualified','proposal','negotiation','won','lost')</td>
                            <td>حالة العميل المحتمل</td>
                        </tr>
                        <tr class="optional">
                            <td>interest_level</td>
                            <td>ENUM</td>
                            <td>('low','medium','high')</td>
                            <td>مستوى الاهتمام</td>
                        </tr>
                        <tr class="optional">
                            <td>estimated_value</td>
                            <td>DECIMAL(12,2)</td>
                            <td>DEFAULT 0.00</td>
                            <td>القيمة المتوقعة</td>
                        </tr>
                        <tr class="optional">
                            <td>expected_close_date</td>
                            <td>DATE</td>
                            <td>NULLABLE</td>
                            <td>تاريخ الإغلاق المتوقع</td>
                        </tr>
                        <tr class="optional">
                            <td>notes</td>
                            <td>TEXT</td>
                            <td>NULLABLE</td>
                            <td>ملاحظات</td>
                        </tr>
                        <tr class="optional">
                            <td>converted_to_customer_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NULLABLE, FOREIGN KEY</td>
                            <td>معرف العميل بعد التحويل</td>
                        </tr>
                        <tr class="optional">
                            <td>converted_at</td>
                            <td>TIMESTAMP</td>
                            <td>NULLABLE</td>
                            <td>تاريخ التحويل</td>
                        </tr>
                        <tr class="required">
                            <td>created_at</td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP</td>
                            <td>تاريخ الإنشاء</td>
                        </tr>
                        <tr class="required">
                            <td>updated_at</td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP ON UPDATE</td>
                            <td>تاريخ التحديث</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="sql-code">
<span class="sql-comment">-- جدول العملاء المحتملين</span>
<span class="sql-keyword">CREATE TABLE</span> leads (
    id <span class="sql-keyword">BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT</span>,
    company_id <span class="sql-keyword">BIGINT UNSIGNED NOT NULL</span>,
    assigned_to <span class="sql-keyword">BIGINT UNSIGNED</span>,
    lead_code <span class="sql-keyword">VARCHAR</span>(50) <span class="sql-keyword">NOT NULL</span>,
    name <span class="sql-keyword">VARCHAR</span>(255) <span class="sql-keyword">NOT NULL</span>,
    company_name <span class="sql-keyword">VARCHAR</span>(255),
    phone <span class="sql-keyword">VARCHAR</span>(20),
    email <span class="sql-keyword">VARCHAR</span>(255),
    address <span class="sql-keyword">TEXT</span>,
    lead_source <span class="sql-keyword">ENUM</span>(<span class="sql-string">'website','phone','referral','social_media','advertisement','walk_in','other'</span>) <span class="sql-keyword">DEFAULT</span> <span class="sql-string">'other'</span>,
    lead_status <span class="sql-keyword">ENUM</span>(<span class="sql-string">'new','contacted','qualified','proposal','negotiation','won','lost'</span>) <span class="sql-keyword">DEFAULT</span> <span class="sql-string">'new'</span>,
    interest_level <span class="sql-keyword">ENUM</span>(<span class="sql-string">'low','medium','high'</span>) <span class="sql-keyword">DEFAULT</span> <span class="sql-string">'medium'</span>,
    estimated_value <span class="sql-keyword">DECIMAL</span>(12,2) <span class="sql-keyword">DEFAULT</span> 0.00,
    expected_close_date <span class="sql-keyword">DATE</span>,
    notes <span class="sql-keyword">TEXT</span>,
    converted_to_customer_id <span class="sql-keyword">BIGINT UNSIGNED</span>,
    converted_at <span class="sql-keyword">TIMESTAMP</span>,
    created_at <span class="sql-keyword">TIMESTAMP DEFAULT CURRENT_TIMESTAMP</span>,
    updated_at <span class="sql-keyword">TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (company_id) <span class="sql-keyword">REFERENCES</span> companies(id) <span class="sql-keyword">ON DELETE CASCADE</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (assigned_to) <span class="sql-keyword">REFERENCES</span> users(id) <span class="sql-keyword">ON DELETE SET NULL</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (converted_to_customer_id) <span class="sql-keyword">REFERENCES</span> customers(id) <span class="sql-keyword">ON DELETE SET NULL</span>,
    <span class="sql-keyword">UNIQUE KEY</span> unique_lead_code (company_id, lead_code),
    <span class="sql-keyword">INDEX</span> idx_lead_status (lead_status),
    <span class="sql-keyword">INDEX</span> idx_lead_source (lead_source),
    <span class="sql-keyword">INDEX</span> idx_assigned_to (assigned_to)
);
            </div>

            <h3>6.2 جدول الفرص التجارية (opportunities)</h3>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>اسم الحقل</th>
                            <th>نوع البيانات</th>
                            <th>القيود</th>
                            <th>الوصف</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="primary-key">
                            <td>id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>PRIMARY KEY, AUTO_INCREMENT</td>
                            <td>المعرف الفريد للفرصة</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>company_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NOT NULL, FOREIGN KEY</td>
                            <td>معرف الشركة</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>lead_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NULLABLE, FOREIGN KEY</td>
                            <td>معرف العميل المحتمل</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>customer_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NULLABLE, FOREIGN KEY</td>
                            <td>معرف العميل</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>assigned_to</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NULLABLE, FOREIGN KEY</td>
                            <td>معرف المندوب المسؤول</td>
                        </tr>
                        <tr class="required">
                            <td>opportunity_code</td>
                            <td>VARCHAR(50)</td>
                            <td>NOT NULL</td>
                            <td>رمز الفرصة</td>
                        </tr>
                        <tr class="required">
                            <td>title</td>
                            <td>VARCHAR(255)</td>
                            <td>NOT NULL</td>
                            <td>عنوان الفرصة</td>
                        </tr>
                        <tr class="optional">
                            <td>description</td>
                            <td>TEXT</td>
                            <td>NULLABLE</td>
                            <td>وصف الفرصة</td>
                        </tr>
                        <tr class="required">
                            <td>stage</td>
                            <td>ENUM</td>
                            <td>('prospecting','qualification','needs_analysis','proposal','negotiation','closed_won','closed_lost')</td>
                            <td>مرحلة الفرصة</td>
                        </tr>
                        <tr class="required">
                            <td>probability</td>
                            <td>INT</td>
                            <td>DEFAULT 0</td>
                            <td>احتمالية النجاح (%)</td>
                        </tr>
                        <tr class="required">
                            <td>estimated_value</td>
                            <td>DECIMAL(12,2)</td>
                            <td>DEFAULT 0.00</td>
                            <td>القيمة المتوقعة</td>
                        </tr>
                        <tr class="required">
                            <td>weighted_value</td>
                            <td>DECIMAL(12,2)</td>
                            <td>DEFAULT 0.00</td>
                            <td>القيمة المرجحة</td>
                        </tr>
                        <tr class="optional">
                            <td>expected_close_date</td>
                            <td>DATE</td>
                            <td>NULLABLE</td>
                            <td>تاريخ الإغلاق المتوقع</td>
                        </tr>
                        <tr class="optional">
                            <td>actual_close_date</td>
                            <td>DATE</td>
                            <td>NULLABLE</td>
                            <td>تاريخ الإغلاق الفعلي</td>
                        </tr>
                        <tr class="optional">
                            <td>close_reason</td>
                            <td>VARCHAR(255)</td>
                            <td>NULLABLE</td>
                            <td>سبب الإغلاق</td>
                        </tr>
                        <tr class="optional">
                            <td>competitor</td>
                            <td>VARCHAR(255)</td>
                            <td>NULLABLE</td>
                            <td>المنافس</td>
                        </tr>
                        <tr class="optional">
                            <td>next_action</td>
                            <td>VARCHAR(255)</td>
                            <td>NULLABLE</td>
                            <td>الإجراء التالي</td>
                        </tr>
                        <tr class="optional">
                            <td>next_action_date</td>
                            <td>DATE</td>
                            <td>NULLABLE</td>
                            <td>تاريخ الإجراء التالي</td>
                        </tr>
                        <tr class="required">
                            <td>created_at</td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP</td>
                            <td>تاريخ الإنشاء</td>
                        </tr>
                        <tr class="required">
                            <td>updated_at</td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP ON UPDATE</td>
                            <td>تاريخ التحديث</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="sql-code">
<span class="sql-comment">-- جدول الفرص التجارية</span>
<span class="sql-keyword">CREATE TABLE</span> opportunities (
    id <span class="sql-keyword">BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT</span>,
    company_id <span class="sql-keyword">BIGINT UNSIGNED NOT NULL</span>,
    lead_id <span class="sql-keyword">BIGINT UNSIGNED</span>,
    customer_id <span class="sql-keyword">BIGINT UNSIGNED</span>,
    assigned_to <span class="sql-keyword">BIGINT UNSIGNED</span>,
    opportunity_code <span class="sql-keyword">VARCHAR</span>(50) <span class="sql-keyword">NOT NULL</span>,
    title <span class="sql-keyword">VARCHAR</span>(255) <span class="sql-keyword">NOT NULL</span>,
    description <span class="sql-keyword">TEXT</span>,
    stage <span class="sql-keyword">ENUM</span>(<span class="sql-string">'prospecting','qualification','needs_analysis','proposal','negotiation','closed_won','closed_lost'</span>) <span class="sql-keyword">DEFAULT</span> <span class="sql-string">'prospecting'</span>,
    probability <span class="sql-keyword">INT DEFAULT</span> 0,
    estimated_value <span class="sql-keyword">DECIMAL</span>(12,2) <span class="sql-keyword">DEFAULT</span> 0.00,
    weighted_value <span class="sql-keyword">DECIMAL</span>(12,2) <span class="sql-keyword">DEFAULT</span> 0.00,
    expected_close_date <span class="sql-keyword">DATE</span>,
    actual_close_date <span class="sql-keyword">DATE</span>,
    close_reason <span class="sql-keyword">VARCHAR</span>(255),
    competitor <span class="sql-keyword">VARCHAR</span>(255),
    next_action <span class="sql-keyword">VARCHAR</span>(255),
    next_action_date <span class="sql-keyword">DATE</span>,
    created_at <span class="sql-keyword">TIMESTAMP DEFAULT CURRENT_TIMESTAMP</span>,
    updated_at <span class="sql-keyword">TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (company_id) <span class="sql-keyword">REFERENCES</span> companies(id) <span class="sql-keyword">ON DELETE CASCADE</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (lead_id) <span class="sql-keyword">REFERENCES</span> leads(id) <span class="sql-keyword">ON DELETE SET NULL</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (customer_id) <span class="sql-keyword">REFERENCES</span> customers(id) <span class="sql-keyword">ON DELETE SET NULL</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (assigned_to) <span class="sql-keyword">REFERENCES</span> users(id) <span class="sql-keyword">ON DELETE SET NULL</span>,
    <span class="sql-keyword">UNIQUE KEY</span> unique_opportunity_code (company_id, opportunity_code),
    <span class="sql-keyword">INDEX</span> idx_opportunity_stage (stage),
    <span class="sql-keyword">INDEX</span> idx_opportunity_assigned (assigned_to),
    <span class="sql-keyword">INDEX</span> idx_opportunity_dates (expected_close_date)
);
            </div>

            <h3>6.3 جدول الأنشطة (activities)</h3>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>اسم الحقل</th>
                            <th>نوع البيانات</th>
                            <th>القيود</th>
                            <th>الوصف</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="primary-key">
                            <td>id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>PRIMARY KEY, AUTO_INCREMENT</td>
                            <td>المعرف الفريد للنشاط</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>company_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NOT NULL, FOREIGN KEY</td>
                            <td>معرف الشركة</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>user_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NOT NULL, FOREIGN KEY</td>
                            <td>معرف المستخدم</td>
                        </tr>
                        <tr class="required">
                            <td>activity_type</td>
                            <td>ENUM</td>
                            <td>('call','email','meeting','task','note','appointment')</td>
                            <td>نوع النشاط</td>
                        </tr>
                        <tr class="required">
                            <td>subject</td>
                            <td>VARCHAR(255)</td>
                            <td>NOT NULL</td>
                            <td>موضوع النشاط</td>
                        </tr>
                        <tr class="optional">
                            <td>description</td>
                            <td>TEXT</td>
                            <td>NULLABLE</td>
                            <td>وصف النشاط</td>
                        </tr>
                        <tr class="required">
                            <td>related_type</td>
                            <td>VARCHAR(50)</td>
                            <td>NOT NULL</td>
                            <td>نوع الكائن المرتبط</td>
                        </tr>
                        <tr class="required">
                            <td>related_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NOT NULL</td>
                            <td>معرف الكائن المرتبط</td>
                        </tr>
                        <tr class="required">
                            <td>activity_date</td>
                            <td>DATETIME</td>
                            <td>NOT NULL</td>
                            <td>تاريخ ووقت النشاط</td>
                        </tr>
                        <tr class="optional">
                            <td>duration_minutes</td>
                            <td>INT</td>
                            <td>DEFAULT 0</td>
                            <td>مدة النشاط بالدقائق</td>
                        </tr>
                        <tr class="required">
                            <td>status</td>
                            <td>ENUM</td>
                            <td>('planned','completed','cancelled')</td>
                            <td>حالة النشاط</td>
                        </tr>
                        <tr class="optional">
                            <td>outcome</td>
                            <td>TEXT</td>
                            <td>NULLABLE</td>
                            <td>نتيجة النشاط</td>
                        </tr>
                        <tr class="required">
                            <td>created_at</td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP</td>
                            <td>تاريخ الإنشاء</td>
                        </tr>
                        <tr class="required">
                            <td>updated_at</td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP ON UPDATE</td>
                            <td>تاريخ التحديث</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="sql-code">
<span class="sql-comment">-- جدول الأنشطة</span>
<span class="sql-keyword">CREATE TABLE</span> activities (
    id <span class="sql-keyword">BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT</span>,
    company_id <span class="sql-keyword">BIGINT UNSIGNED NOT NULL</span>,
    user_id <span class="sql-keyword">BIGINT UNSIGNED NOT NULL</span>,
    activity_type <span class="sql-keyword">ENUM</span>(<span class="sql-string">'call','email','meeting','task','note','appointment'</span>) <span class="sql-keyword">NOT NULL</span>,
    subject <span class="sql-keyword">VARCHAR</span>(255) <span class="sql-keyword">NOT NULL</span>,
    description <span class="sql-keyword">TEXT</span>,
    related_type <span class="sql-keyword">VARCHAR</span>(50) <span class="sql-keyword">NOT NULL</span>,
    related_id <span class="sql-keyword">BIGINT UNSIGNED NOT NULL</span>,
    activity_date <span class="sql-keyword">DATETIME NOT NULL</span>,
    duration_minutes <span class="sql-keyword">INT DEFAULT</span> 0,
    status <span class="sql-keyword">ENUM</span>(<span class="sql-string">'planned','completed','cancelled'</span>) <span class="sql-keyword">DEFAULT</span> <span class="sql-string">'planned'</span>,
    outcome <span class="sql-keyword">TEXT</span>,
    created_at <span class="sql-keyword">TIMESTAMP DEFAULT CURRENT_TIMESTAMP</span>,
    updated_at <span class="sql-keyword">TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (company_id) <span class="sql-keyword">REFERENCES</span> companies(id) <span class="sql-keyword">ON DELETE CASCADE</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (user_id) <span class="sql-keyword">REFERENCES</span> users(id),
    <span class="sql-keyword">INDEX</span> idx_activity_type (activity_type),
    <span class="sql-keyword">INDEX</span> idx_activity_date (activity_date),
    <span class="sql-keyword">INDEX</span> idx_activity_status (status),
    <span class="sql-keyword">INDEX</span> idx_related_object (related_type, related_id),
    <span class="sql-keyword">INDEX</span> idx_user_activities (user_id)
);
            </div>
        </div>
    </div>
</body>
</html>
