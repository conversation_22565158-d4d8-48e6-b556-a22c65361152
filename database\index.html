<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فهرس جداول قاعدة البيانات - نظام ERP للخياطة</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            margin-top: 20px;
            margin-bottom: 20px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            padding: 40px 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 3em;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .overview {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 40px;
            border: 1px solid #e9ecef;
        }
        
        .overview h2 {
            color: #495057;
            font-size: 2em;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-left: 4px solid #667eea;
        }
        
        .stat-number {
            font-size: 2.5em;
            font-weight: 700;
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .stat-label {
            color: #6c757d;
            font-weight: 500;
        }
        
        .modules-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }
        
        .module-card {
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        
        .module-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }
        
        .module-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            text-align: center;
        }
        
        .module-header h3 {
            font-size: 1.5em;
            font-weight: 600;
            margin-bottom: 10px;
        }
        
        .module-header p {
            opacity: 0.9;
            font-size: 0.95em;
        }
        
        .module-content {
            padding: 25px;
        }
        
        .table-list {
            list-style: none;
            margin-bottom: 20px;
        }
        
        .table-list li {
            padding: 8px 0;
            border-bottom: 1px solid #f1f3f4;
            color: #495057;
            font-size: 0.9em;
        }
        
        .table-list li:last-child {
            border-bottom: none;
        }
        
        .table-list li::before {
            content: "📊";
            margin-left: 10px;
        }
        
        .module-link {
            display: inline-block;
            width: 100%;
            padding: 12px 20px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            text-align: center;
            font-weight: 500;
            transition: background 0.3s ease;
        }
        
        .module-link:hover {
            background: #5a67d8;
        }
        
        .nav-home {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .nav-home a {
            display: inline-block;
            padding: 15px 30px;
            background: #28a745;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            font-size: 1.1em;
        }
        
        .nav-home a:hover {
            background: #218838;
        }
        
        .features {
            background: #e3f2fd;
            padding: 25px;
            border-radius: 10px;
            margin: 30px 0;
            border-left: 5px solid #2196f3;
        }
        
        .features h3 {
            color: #1976d2;
            margin-bottom: 15px;
        }
        
        .features ul {
            list-style: none;
        }
        
        .features li {
            padding: 5px 0;
            color: #424242;
        }
        
        .features li::before {
            content: "✅";
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>فهرس جداول قاعدة البيانات</h1>
            <p>نظام ERP متكامل لمعامل ومحلات الخياطة</p>
        </div>

        <div class="nav-home">
            <a href="../index.html">🏠 العودة للصفحة الرئيسية</a>
        </div>

        <div class="overview">
            <h2>نظرة عامة على قاعدة البيانات</h2>
            <p style="text-align: center; color: #6c757d; margin-bottom: 30px;">
                تم تصميم قاعدة البيانات وفقاً لأفضل الممارسات في أنظمة ERP العالمية مع التركيز على احتياجات معامل الخياطة
            </p>
            
            <div class="stats">
                <div class="stat-card">
                    <div class="stat-number">8</div>
                    <div class="stat-label">مجموعات جداول</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">50+</div>
                    <div class="stat-label">جدول قاعدة بيانات</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">7</div>
                    <div class="stat-label">موديولات رئيسية</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">100%</div>
                    <div class="stat-label">متوافق مع Laravel</div>
                </div>
            </div>
        </div>

        <div class="features">
            <h3>الميزات الرئيسية لتصميم قاعدة البيانات:</h3>
            <ul>
                <li>دعم الشركات المتعددة (Multi-tenant)</li>
                <li>نظام صلاحيات متقدم (RBAC)</li>
                <li>تتبع كامل للمخزون عبر الفروع</li>
                <li>دعم العمل offline لنقطة البيع</li>
                <li>نظام محاسبي متكامل</li>
                <li>إدارة دورة التصنيع الكاملة</li>
                <li>نظام CRM شامل</li>
                <li>تدقيق كامل للعمليات</li>
            </ul>
        </div>

        <div class="modules-grid">
            <div class="module-card">
                <div class="module-header">
                    <h3>الجداول الأساسية</h3>
                    <p>الشركات، الفروع، المستخدمين</p>
                </div>
                <div class="module-content">
                    <ul class="table-list">
                        <li>companies - الشركات</li>
                        <li>branches - الفروع</li>
                        <li>users - المستخدمين</li>
                    </ul>
                    <a href="01_core_tables.html" class="module-link">عرض التفاصيل</a>
                </div>
            </div>

            <div class="module-card">
                <div class="module-header">
                    <h3>المنتجات والمخزون</h3>
                    <p>إدارة شاملة للمنتجات والمخزون</p>
                </div>
                <div class="module-content">
                    <ul class="table-list">
                        <li>product_categories - فئات المنتجات</li>
                        <li>units_of_measure - وحدات القياس</li>
                        <li>products - المنتجات</li>
                        <li>product_variants - متغيرات المنتجات</li>
                        <li>inventory_locations - مواقع المخزون</li>
                        <li>stock_movements - حركات المخزون</li>
                    </ul>
                    <a href="02_product_inventory_tables.html" class="module-link">عرض التفاصيل</a>
                </div>
            </div>

            <div class="module-card">
                <div class="module-header">
                    <h3>المبيعات ونقطة البيع</h3>
                    <p>إدارة العملاء والمبيعات والفواتير</p>
                </div>
                <div class="module-content">
                    <ul class="table-list">
                        <li>customers - العملاء</li>
                        <li>sales_orders - أوامر البيع</li>
                        <li>invoices - الفواتير</li>
                        <li>pos_transactions - معاملات نقطة البيع</li>
                        <li>payments - المدفوعات</li>
                        <li>returns - المرتجعات</li>
                    </ul>
                    <a href="03_sales_pos_tables.html" class="module-link">عرض التفاصيل</a>
                </div>
            </div>

            <div class="module-card">
                <div class="module-header">
                    <h3>المحاسبة</h3>
                    <p>نظام محاسبي متكامل</p>
                </div>
                <div class="module-content">
                    <ul class="table-list">
                        <li>chart_of_accounts - دليل الحسابات</li>
                        <li>journal_entries - القيود المحاسبية</li>
                        <li>fiscal_years - السنوات المالية</li>
                        <li>tax_configurations - إعدادات الضرائب</li>
                        <li>financial_reports - التقارير المالية</li>
                    </ul>
                    <a href="04_accounting_tables.html" class="module-link">عرض التفاصيل</a>
                </div>
            </div>

            <div class="module-card">
                <div class="module-header">
                    <h3>التصنيع</h3>
                    <p>إدارة عمليات التصنيع والإنتاج</p>
                </div>
                <div class="module-content">
                    <ul class="table-list">
                        <li>bill_of_materials - قوائم المواد</li>
                        <li>work_orders - أوامر العمل</li>
                        <li>production_stages - مراحل الإنتاج</li>
                        <li>job_cards - بطاقات العمل</li>
                        <li>manufacturing_costs - تكاليف التصنيع</li>
                    </ul>
                    <a href="05_manufacturing_tables.html" class="module-link">عرض التفاصيل</a>
                </div>
            </div>

            <div class="module-card">
                <div class="module-header">
                    <h3>إدارة العملاء (CRM)</h3>
                    <p>إدارة علاقات العملاء والفرص</p>
                </div>
                <div class="module-content">
                    <ul class="table-list">
                        <li>leads - العملاء المحتملين</li>
                        <li>opportunities - الفرص التجارية</li>
                        <li>activities - الأنشطة</li>
                        <li>tasks - المهام</li>
                        <li>follow_ups - المتابعات</li>
                    </ul>
                    <a href="06_crm_tables.html" class="module-link">عرض التفاصيل</a>
                </div>
            </div>

            <div class="module-card">
                <div class="module-header">
                    <h3>المشتريات والموردين</h3>
                    <p>إدارة الموردين وعمليات الشراء</p>
                </div>
                <div class="module-content">
                    <ul class="table-list">
                        <li>suppliers - الموردين</li>
                        <li>purchase_orders - أوامر الشراء</li>
                        <li>goods_receipts - إيصالات الاستلام</li>
                        <li>purchase_invoices - فواتير الشراء</li>
                        <li>supplier_evaluations - تقييم الموردين</li>
                    </ul>
                    <a href="07_purchasing_tables.html" class="module-link">عرض التفاصيل</a>
                </div>
            </div>

            <div class="module-card">
                <div class="module-header">
                    <h3>جداول النظام</h3>
                    <p>الصلاحيات والإعدادات والتدقيق</p>
                </div>
                <div class="module-content">
                    <ul class="table-list">
                        <li>roles - الأدوار</li>
                        <li>permissions - الصلاحيات</li>
                        <li>audit_logs - سجلات التدقيق</li>
                        <li>system_settings - إعدادات النظام</li>
                        <li>notifications - الإشعارات</li>
                        <li>file_attachments - المرفقات</li>
                    </ul>
                    <a href="08_system_tables.html" class="module-link">عرض التفاصيل</a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
