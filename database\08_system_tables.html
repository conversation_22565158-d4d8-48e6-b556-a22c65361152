<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>جداول النظام - نظام ERP للخياطة</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            margin-top: 20px;
            margin-bottom: 20px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            padding: 40px 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 2.5em;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .section {
            margin-bottom: 50px;
            padding: 30px;
            background: #f8f9fa;
            border-radius: 15px;
            border: 1px solid #e9ecef;
        }
        
        .section h2 {
            color: #495057;
            font-size: 2em;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 3px solid #667eea;
        }
        
        .section h3 {
            color: #6c757d;
            font-size: 1.5em;
            margin: 25px 0 15px 0;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border-left: 4px solid #28a745;
        }
        
        .table-container {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin: 20px 0;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            padding: 12px 15px;
            text-align: right;
            border-bottom: 1px solid #dee2e6;
            font-size: 0.9em;
        }
        
        th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: 600;
            position: sticky;
            top: 0;
        }
        
        tr:hover {
            background: #f8f9fa;
        }
        
        .primary-key {
            background: #fff3cd;
            font-weight: bold;
        }
        
        .foreign-key {
            background: #d1ecf1;
        }
        
        .required {
            color: #dc3545;
            font-weight: bold;
        }
        
        .optional {
            color: #6c757d;
        }
        
        .sql-code {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.85em;
            overflow-x: auto;
            margin: 20px 0;
            white-space: pre-wrap;
        }
        
        .sql-keyword {
            color: #63b3ed;
            font-weight: bold;
        }
        
        .sql-string {
            color: #68d391;
        }
        
        .sql-comment {
            color: #a0aec0;
            font-style: italic;
        }
        
        .info-box {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-left: 5px solid #17a2b8;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .nav-links {
            background: #e9ecef;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .nav-links a {
            display: inline-block;
            margin: 5px 10px;
            padding: 10px 20px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-weight: 500;
        }
        
        .nav-links a:hover {
            background: #5a67d8;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>جداول النظام</h1>
            <p>نظام ERP لمعامل ومحلات الخياطة</p>
        </div>

        <div class="nav-links">
            <a href="../index.html">الصفحة الرئيسية</a>
            <a href="01_core_tables.html">الجداول الأساسية</a>
            <a href="02_product_inventory_tables.html">المنتجات والمخزون</a>
            <a href="03_sales_pos_tables.html">المبيعات ونقطة البيع</a>
            <a href="04_accounting_tables.html">المحاسبة</a>
            <a href="05_manufacturing_tables.html">التصنيع</a>
            <a href="06_crm_tables.html">إدارة العملاء</a>
            <a href="07_purchasing_tables.html">المشتريات والموردين</a>
            <a href="08_system_tables.html">جداول النظام</a>
        </div>

        <div class="section">
            <h2>8. جداول النظام</h2>
            
            <div class="info-box">
                <h4>نظرة عامة:</h4>
                <p>هذه الجداول تدير الوظائف الأساسية للنظام مثل الصلاحيات، سجلات التدقيق، الإعدادات، والإشعارات.</p>
            </div>

            <h3>8.1 جدول الأدوار (roles)</h3>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>اسم الحقل</th>
                            <th>نوع البيانات</th>
                            <th>القيود</th>
                            <th>الوصف</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="primary-key">
                            <td>id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>PRIMARY KEY, AUTO_INCREMENT</td>
                            <td>المعرف الفريد للدور</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>company_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NOT NULL, FOREIGN KEY</td>
                            <td>معرف الشركة</td>
                        </tr>
                        <tr class="required">
                            <td>name</td>
                            <td>VARCHAR(100)</td>
                            <td>NOT NULL</td>
                            <td>اسم الدور</td>
                        </tr>
                        <tr class="required">
                            <td>slug</td>
                            <td>VARCHAR(100)</td>
                            <td>NOT NULL</td>
                            <td>الاسم المختصر</td>
                        </tr>
                        <tr class="optional">
                            <td>description</td>
                            <td>TEXT</td>
                            <td>NULLABLE</td>
                            <td>وصف الدور</td>
                        </tr>
                        <tr class="required">
                            <td>is_system_role</td>
                            <td>BOOLEAN</td>
                            <td>DEFAULT FALSE</td>
                            <td>دور نظام</td>
                        </tr>
                        <tr class="required">
                            <td>is_active</td>
                            <td>BOOLEAN</td>
                            <td>DEFAULT TRUE</td>
                            <td>حالة النشاط</td>
                        </tr>
                        <tr class="required">
                            <td>created_at</td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP</td>
                            <td>تاريخ الإنشاء</td>
                        </tr>
                        <tr class="required">
                            <td>updated_at</td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP ON UPDATE</td>
                            <td>تاريخ التحديث</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="sql-code">
<span class="sql-comment">-- جدول الأدوار</span>
<span class="sql-keyword">CREATE TABLE</span> roles (
    id <span class="sql-keyword">BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT</span>,
    company_id <span class="sql-keyword">BIGINT UNSIGNED NOT NULL</span>,
    name <span class="sql-keyword">VARCHAR</span>(100) <span class="sql-keyword">NOT NULL</span>,
    slug <span class="sql-keyword">VARCHAR</span>(100) <span class="sql-keyword">NOT NULL</span>,
    description <span class="sql-keyword">TEXT</span>,
    is_system_role <span class="sql-keyword">BOOLEAN DEFAULT FALSE</span>,
    is_active <span class="sql-keyword">BOOLEAN DEFAULT TRUE</span>,
    created_at <span class="sql-keyword">TIMESTAMP DEFAULT CURRENT_TIMESTAMP</span>,
    updated_at <span class="sql-keyword">TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (company_id) <span class="sql-keyword">REFERENCES</span> companies(id) <span class="sql-keyword">ON DELETE CASCADE</span>,
    <span class="sql-keyword">UNIQUE KEY</span> unique_role_slug (company_id, slug),
    <span class="sql-keyword">INDEX</span> idx_role_active (is_active)
);
            </div>

            <h3>8.2 جدول الصلاحيات (permissions)</h3>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>اسم الحقل</th>
                            <th>نوع البيانات</th>
                            <th>القيود</th>
                            <th>الوصف</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="primary-key">
                            <td>id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>PRIMARY KEY, AUTO_INCREMENT</td>
                            <td>المعرف الفريد للصلاحية</td>
                        </tr>
                        <tr class="required">
                            <td>name</td>
                            <td>VARCHAR(100)</td>
                            <td>NOT NULL</td>
                            <td>اسم الصلاحية</td>
                        </tr>
                        <tr class="required">
                            <td>slug</td>
                            <td>VARCHAR(100)</td>
                            <td>NOT NULL, UNIQUE</td>
                            <td>الاسم المختصر</td>
                        </tr>
                        <tr class="required">
                            <td>module</td>
                            <td>VARCHAR(50)</td>
                            <td>NOT NULL</td>
                            <td>الموديول</td>
                        </tr>
                        <tr class="required">
                            <td>action</td>
                            <td>ENUM</td>
                            <td>('create','read','update','delete','manage')</td>
                            <td>نوع العملية</td>
                        </tr>
                        <tr class="optional">
                            <td>description</td>
                            <td>TEXT</td>
                            <td>NULLABLE</td>
                            <td>وصف الصلاحية</td>
                        </tr>
                        <tr class="required">
                            <td>is_system_permission</td>
                            <td>BOOLEAN</td>
                            <td>DEFAULT FALSE</td>
                            <td>صلاحية نظام</td>
                        </tr>
                        <tr class="required">
                            <td>created_at</td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP</td>
                            <td>تاريخ الإنشاء</td>
                        </tr>
                        <tr class="required">
                            <td>updated_at</td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP ON UPDATE</td>
                            <td>تاريخ التحديث</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="sql-code">
<span class="sql-comment">-- جدول الصلاحيات</span>
<span class="sql-keyword">CREATE TABLE</span> permissions (
    id <span class="sql-keyword">BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT</span>,
    name <span class="sql-keyword">VARCHAR</span>(100) <span class="sql-keyword">NOT NULL</span>,
    slug <span class="sql-keyword">VARCHAR</span>(100) <span class="sql-keyword">NOT NULL UNIQUE</span>,
    module <span class="sql-keyword">VARCHAR</span>(50) <span class="sql-keyword">NOT NULL</span>,
    action <span class="sql-keyword">ENUM</span>(<span class="sql-string">'create','read','update','delete','manage'</span>) <span class="sql-keyword">NOT NULL</span>,
    description <span class="sql-keyword">TEXT</span>,
    is_system_permission <span class="sql-keyword">BOOLEAN DEFAULT FALSE</span>,
    created_at <span class="sql-keyword">TIMESTAMP DEFAULT CURRENT_TIMESTAMP</span>,
    updated_at <span class="sql-keyword">TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP</span>,
    <span class="sql-keyword">INDEX</span> idx_permission_module (module),
    <span class="sql-keyword">INDEX</span> idx_permission_action (action)
);
            </div>

            <h3>8.3 جدول ربط الأدوار بالصلاحيات (role_permissions)</h3>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>اسم الحقل</th>
                            <th>نوع البيانات</th>
                            <th>القيود</th>
                            <th>الوصف</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="primary-key">
                            <td>id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>PRIMARY KEY, AUTO_INCREMENT</td>
                            <td>المعرف الفريد للربط</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>role_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NOT NULL, FOREIGN KEY</td>
                            <td>معرف الدور</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>permission_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NOT NULL, FOREIGN KEY</td>
                            <td>معرف الصلاحية</td>
                        </tr>
                        <tr class="required">
                            <td>created_at</td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP</td>
                            <td>تاريخ الإنشاء</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="sql-code">
<span class="sql-comment">-- جدول ربط الأدوار بالصلاحيات</span>
<span class="sql-keyword">CREATE TABLE</span> role_permissions (
    id <span class="sql-keyword">BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT</span>,
    role_id <span class="sql-keyword">BIGINT UNSIGNED NOT NULL</span>,
    permission_id <span class="sql-keyword">BIGINT UNSIGNED NOT NULL</span>,
    created_at <span class="sql-keyword">TIMESTAMP DEFAULT CURRENT_TIMESTAMP</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (role_id) <span class="sql-keyword">REFERENCES</span> roles(id) <span class="sql-keyword">ON DELETE CASCADE</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (permission_id) <span class="sql-keyword">REFERENCES</span> permissions(id) <span class="sql-keyword">ON DELETE CASCADE</span>,
    <span class="sql-keyword">UNIQUE KEY</span> unique_role_permission (role_id, permission_id)
);
            </div>

            <h3>8.4 جدول سجلات التدقيق (audit_logs)</h3>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>اسم الحقل</th>
                            <th>نوع البيانات</th>
                            <th>القيود</th>
                            <th>الوصف</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="primary-key">
                            <td>id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>PRIMARY KEY, AUTO_INCREMENT</td>
                            <td>المعرف الفريد للسجل</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>company_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NOT NULL, FOREIGN KEY</td>
                            <td>معرف الشركة</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>user_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NULLABLE, FOREIGN KEY</td>
                            <td>معرف المستخدم</td>
                        </tr>
                        <tr class="required">
                            <td>action</td>
                            <td>VARCHAR(50)</td>
                            <td>NOT NULL</td>
                            <td>نوع العملية</td>
                        </tr>
                        <tr class="required">
                            <td>model_type</td>
                            <td>VARCHAR(100)</td>
                            <td>NOT NULL</td>
                            <td>نوع النموذج</td>
                        </tr>
                        <tr class="required">
                            <td>model_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NOT NULL</td>
                            <td>معرف النموذج</td>
                        </tr>
                        <tr class="optional">
                            <td>old_values</td>
                            <td>JSON</td>
                            <td>NULLABLE</td>
                            <td>القيم القديمة</td>
                        </tr>
                        <tr class="optional">
                            <td>new_values</td>
                            <td>JSON</td>
                            <td>NULLABLE</td>
                            <td>القيم الجديدة</td>
                        </tr>
                        <tr class="required">
                            <td>ip_address</td>
                            <td>VARCHAR(45)</td>
                            <td>NOT NULL</td>
                            <td>عنوان IP</td>
                        </tr>
                        <tr class="optional">
                            <td>user_agent</td>
                            <td>TEXT</td>
                            <td>NULLABLE</td>
                            <td>معلومات المتصفح</td>
                        </tr>
                        <tr class="required">
                            <td>created_at</td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP</td>
                            <td>تاريخ الإنشاء</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="sql-code">
<span class="sql-comment">-- جدول سجلات التدقيق</span>
<span class="sql-keyword">CREATE TABLE</span> audit_logs (
    id <span class="sql-keyword">BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT</span>,
    company_id <span class="sql-keyword">BIGINT UNSIGNED NOT NULL</span>,
    user_id <span class="sql-keyword">BIGINT UNSIGNED</span>,
    action <span class="sql-keyword">VARCHAR</span>(50) <span class="sql-keyword">NOT NULL</span>,
    model_type <span class="sql-keyword">VARCHAR</span>(100) <span class="sql-keyword">NOT NULL</span>,
    model_id <span class="sql-keyword">BIGINT UNSIGNED NOT NULL</span>,
    old_values <span class="sql-keyword">JSON</span>,
    new_values <span class="sql-keyword">JSON</span>,
    ip_address <span class="sql-keyword">VARCHAR</span>(45) <span class="sql-keyword">NOT NULL</span>,
    user_agent <span class="sql-keyword">TEXT</span>,
    created_at <span class="sql-keyword">TIMESTAMP DEFAULT CURRENT_TIMESTAMP</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (company_id) <span class="sql-keyword">REFERENCES</span> companies(id) <span class="sql-keyword">ON DELETE CASCADE</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (user_id) <span class="sql-keyword">REFERENCES</span> users(id) <span class="sql-keyword">ON DELETE SET NULL</span>,
    <span class="sql-keyword">INDEX</span> idx_audit_action (action),
    <span class="sql-keyword">INDEX</span> idx_audit_model (model_type, model_id),
    <span class="sql-keyword">INDEX</span> idx_audit_user (user_id),
    <span class="sql-keyword">INDEX</span> idx_audit_date (created_at)
);
            </div>

            <h3>8.5 جدول إعدادات النظام (system_settings)</h3>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>اسم الحقل</th>
                            <th>نوع البيانات</th>
                            <th>القيود</th>
                            <th>الوصف</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="primary-key">
                            <td>id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>PRIMARY KEY, AUTO_INCREMENT</td>
                            <td>المعرف الفريد للإعداد</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>company_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NULLABLE, FOREIGN KEY</td>
                            <td>معرف الشركة (NULL للإعدادات العامة)</td>
                        </tr>
                        <tr class="required">
                            <td>setting_key</td>
                            <td>VARCHAR(100)</td>
                            <td>NOT NULL</td>
                            <td>مفتاح الإعداد</td>
                        </tr>
                        <tr class="required">
                            <td>setting_value</td>
                            <td>TEXT</td>
                            <td>NOT NULL</td>
                            <td>قيمة الإعداد</td>
                        </tr>
                        <tr class="required">
                            <td>setting_type</td>
                            <td>ENUM</td>
                            <td>('string','integer','boolean','json','array')</td>
                            <td>نوع الإعداد</td>
                        </tr>
                        <tr class="optional">
                            <td>description</td>
                            <td>TEXT</td>
                            <td>NULLABLE</td>
                            <td>وصف الإعداد</td>
                        </tr>
                        <tr class="required">
                            <td>is_public</td>
                            <td>BOOLEAN</td>
                            <td>DEFAULT FALSE</td>
                            <td>إعداد عام</td>
                        </tr>
                        <tr class="required">
                            <td>created_at</td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP</td>
                            <td>تاريخ الإنشاء</td>
                        </tr>
                        <tr class="required">
                            <td>updated_at</td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP ON UPDATE</td>
                            <td>تاريخ التحديث</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="sql-code">
<span class="sql-comment">-- جدول إعدادات النظام</span>
<span class="sql-keyword">CREATE TABLE</span> system_settings (
    id <span class="sql-keyword">BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT</span>,
    company_id <span class="sql-keyword">BIGINT UNSIGNED</span>,
    setting_key <span class="sql-keyword">VARCHAR</span>(100) <span class="sql-keyword">NOT NULL</span>,
    setting_value <span class="sql-keyword">TEXT NOT NULL</span>,
    setting_type <span class="sql-keyword">ENUM</span>(<span class="sql-string">'string','integer','boolean','json','array'</span>) <span class="sql-keyword">DEFAULT</span> <span class="sql-string">'string'</span>,
    description <span class="sql-keyword">TEXT</span>,
    is_public <span class="sql-keyword">BOOLEAN DEFAULT FALSE</span>,
    created_at <span class="sql-keyword">TIMESTAMP DEFAULT CURRENT_TIMESTAMP</span>,
    updated_at <span class="sql-keyword">TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (company_id) <span class="sql-keyword">REFERENCES</span> companies(id) <span class="sql-keyword">ON DELETE CASCADE</span>,
    <span class="sql-keyword">UNIQUE KEY</span> unique_setting (company_id, setting_key),
    <span class="sql-keyword">INDEX</span> idx_setting_key (setting_key),
    <span class="sql-keyword">INDEX</span> idx_setting_public (is_public)
);
            </div>
        </div>
    </div>
</body>
</html>
