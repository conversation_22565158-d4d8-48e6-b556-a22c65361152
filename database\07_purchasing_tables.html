<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>جداول المشتريات والموردين - نظام ERP للخياطة</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            margin-top: 20px;
            margin-bottom: 20px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            padding: 40px 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 2.5em;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .section {
            margin-bottom: 50px;
            padding: 30px;
            background: #f8f9fa;
            border-radius: 15px;
            border: 1px solid #e9ecef;
        }
        
        .section h2 {
            color: #495057;
            font-size: 2em;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 3px solid #667eea;
        }
        
        .section h3 {
            color: #6c757d;
            font-size: 1.5em;
            margin: 25px 0 15px 0;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border-left: 4px solid #28a745;
        }
        
        .table-container {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin: 20px 0;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            padding: 12px 15px;
            text-align: right;
            border-bottom: 1px solid #dee2e6;
            font-size: 0.9em;
        }
        
        th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: 600;
            position: sticky;
            top: 0;
        }
        
        tr:hover {
            background: #f8f9fa;
        }
        
        .primary-key {
            background: #fff3cd;
            font-weight: bold;
        }
        
        .foreign-key {
            background: #d1ecf1;
        }
        
        .required {
            color: #dc3545;
            font-weight: bold;
        }
        
        .optional {
            color: #6c757d;
        }
        
        .sql-code {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.85em;
            overflow-x: auto;
            margin: 20px 0;
            white-space: pre-wrap;
        }
        
        .sql-keyword {
            color: #63b3ed;
            font-weight: bold;
        }
        
        .sql-string {
            color: #68d391;
        }
        
        .sql-comment {
            color: #a0aec0;
            font-style: italic;
        }
        
        .info-box {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-left: 5px solid #17a2b8;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .nav-links {
            background: #e9ecef;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .nav-links a {
            display: inline-block;
            margin: 5px 10px;
            padding: 10px 20px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-weight: 500;
        }
        
        .nav-links a:hover {
            background: #5a67d8;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>جداول المشتريات والموردين</h1>
            <p>نظام ERP لمعامل ومحلات الخياطة</p>
        </div>

        <div class="nav-links">
            <a href="../index.html">الصفحة الرئيسية</a>
            <a href="01_core_tables.html">الجداول الأساسية</a>
            <a href="02_product_inventory_tables.html">المنتجات والمخزون</a>
            <a href="03_sales_pos_tables.html">المبيعات ونقطة البيع</a>
            <a href="04_accounting_tables.html">المحاسبة</a>
            <a href="05_manufacturing_tables.html">التصنيع</a>
            <a href="06_crm_tables.html">إدارة العملاء</a>
            <a href="07_purchasing_tables.html">المشتريات والموردين</a>
            <a href="08_system_tables.html">جداول النظام</a>
        </div>

        <div class="section">
            <h2>7. جداول المشتريات والموردين</h2>
            
            <div class="info-box">
                <h4>نظرة عامة:</h4>
                <p>هذه الجداول تدير عمليات الشراء الكاملة من إدارة الموردين إلى أوامر الشراء والاستلام والفواتير.</p>
            </div>

            <h3>7.1 جدول الموردين (suppliers)</h3>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>اسم الحقل</th>
                            <th>نوع البيانات</th>
                            <th>القيود</th>
                            <th>الوصف</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="primary-key">
                            <td>id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>PRIMARY KEY, AUTO_INCREMENT</td>
                            <td>المعرف الفريد للمورد</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>company_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NOT NULL, FOREIGN KEY</td>
                            <td>معرف الشركة</td>
                        </tr>
                        <tr class="required">
                            <td>supplier_code</td>
                            <td>VARCHAR(50)</td>
                            <td>NOT NULL</td>
                            <td>رمز المورد</td>
                        </tr>
                        <tr class="required">
                            <td>name</td>
                            <td>VARCHAR(255)</td>
                            <td>NOT NULL</td>
                            <td>اسم المورد</td>
                        </tr>
                        <tr class="required">
                            <td>supplier_type</td>
                            <td>ENUM</td>
                            <td>('individual','company')</td>
                            <td>نوع المورد</td>
                        </tr>
                        <tr class="optional">
                            <td>contact_person</td>
                            <td>VARCHAR(255)</td>
                            <td>NULLABLE</td>
                            <td>الشخص المسؤول</td>
                        </tr>
                        <tr class="optional">
                            <td>phone</td>
                            <td>VARCHAR(20)</td>
                            <td>NULLABLE</td>
                            <td>رقم الهاتف</td>
                        </tr>
                        <tr class="optional">
                            <td>email</td>
                            <td>VARCHAR(255)</td>
                            <td>NULLABLE</td>
                            <td>البريد الإلكتروني</td>
                        </tr>
                        <tr class="optional">
                            <td>address</td>
                            <td>TEXT</td>
                            <td>NULLABLE</td>
                            <td>العنوان</td>
                        </tr>
                        <tr class="optional">
                            <td>city</td>
                            <td>VARCHAR(100)</td>
                            <td>NULLABLE</td>
                            <td>المدينة</td>
                        </tr>
                        <tr class="optional">
                            <td>country</td>
                            <td>VARCHAR(100)</td>
                            <td>NULLABLE</td>
                            <td>البلد</td>
                        </tr>
                        <tr class="optional">
                            <td>tax_number</td>
                            <td>VARCHAR(50)</td>
                            <td>NULLABLE</td>
                            <td>الرقم الضريبي</td>
                        </tr>
                        <tr class="required">
                            <td>payment_terms</td>
                            <td>INT</td>
                            <td>DEFAULT 30</td>
                            <td>شروط الدفع (بالأيام)</td>
                        </tr>
                        <tr class="required">
                            <td>credit_limit</td>
                            <td>DECIMAL(12,2)</td>
                            <td>DEFAULT 0.00</td>
                            <td>حد الائتمان</td>
                        </tr>
                        <tr class="optional">
                            <td>discount_percentage</td>
                            <td>DECIMAL(5,2)</td>
                            <td>DEFAULT 0.00</td>
                            <td>نسبة الخصم</td>
                        </tr>
                        <tr class="required">
                            <td>rating</td>
                            <td>ENUM</td>
                            <td>('excellent','good','average','poor')</td>
                            <td>تقييم المورد</td>
                        </tr>
                        <tr class="required">
                            <td>is_active</td>
                            <td>BOOLEAN</td>
                            <td>DEFAULT TRUE</td>
                            <td>حالة النشاط</td>
                        </tr>
                        <tr class="optional">
                            <td>notes</td>
                            <td>TEXT</td>
                            <td>NULLABLE</td>
                            <td>ملاحظات</td>
                        </tr>
                        <tr class="required">
                            <td>created_at</td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP</td>
                            <td>تاريخ الإنشاء</td>
                        </tr>
                        <tr class="required">
                            <td>updated_at</td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP ON UPDATE</td>
                            <td>تاريخ التحديث</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="sql-code">
<span class="sql-comment">-- جدول الموردين</span>
<span class="sql-keyword">CREATE TABLE</span> suppliers (
    id <span class="sql-keyword">BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT</span>,
    company_id <span class="sql-keyword">BIGINT UNSIGNED NOT NULL</span>,
    supplier_code <span class="sql-keyword">VARCHAR</span>(50) <span class="sql-keyword">NOT NULL</span>,
    name <span class="sql-keyword">VARCHAR</span>(255) <span class="sql-keyword">NOT NULL</span>,
    supplier_type <span class="sql-keyword">ENUM</span>(<span class="sql-string">'individual','company'</span>) <span class="sql-keyword">DEFAULT</span> <span class="sql-string">'company'</span>,
    contact_person <span class="sql-keyword">VARCHAR</span>(255),
    phone <span class="sql-keyword">VARCHAR</span>(20),
    email <span class="sql-keyword">VARCHAR</span>(255),
    address <span class="sql-keyword">TEXT</span>,
    city <span class="sql-keyword">VARCHAR</span>(100),
    country <span class="sql-keyword">VARCHAR</span>(100),
    tax_number <span class="sql-keyword">VARCHAR</span>(50),
    payment_terms <span class="sql-keyword">INT DEFAULT</span> 30,
    credit_limit <span class="sql-keyword">DECIMAL</span>(12,2) <span class="sql-keyword">DEFAULT</span> 0.00,
    discount_percentage <span class="sql-keyword">DECIMAL</span>(5,2) <span class="sql-keyword">DEFAULT</span> 0.00,
    rating <span class="sql-keyword">ENUM</span>(<span class="sql-string">'excellent','good','average','poor'</span>) <span class="sql-keyword">DEFAULT</span> <span class="sql-string">'good'</span>,
    is_active <span class="sql-keyword">BOOLEAN DEFAULT TRUE</span>,
    notes <span class="sql-keyword">TEXT</span>,
    created_at <span class="sql-keyword">TIMESTAMP DEFAULT CURRENT_TIMESTAMP</span>,
    updated_at <span class="sql-keyword">TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (company_id) <span class="sql-keyword">REFERENCES</span> companies(id) <span class="sql-keyword">ON DELETE CASCADE</span>,
    <span class="sql-keyword">UNIQUE KEY</span> unique_supplier_code (company_id, supplier_code),
    <span class="sql-keyword">INDEX</span> idx_supplier_phone (phone),
    <span class="sql-keyword">INDEX</span> idx_supplier_email (email),
    <span class="sql-keyword">INDEX</span> idx_supplier_rating (rating)
);
            </div>

            <h3>7.2 جدول أوامر الشراء (purchase_orders)</h3>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>اسم الحقل</th>
                            <th>نوع البيانات</th>
                            <th>القيود</th>
                            <th>الوصف</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="primary-key">
                            <td>id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>PRIMARY KEY, AUTO_INCREMENT</td>
                            <td>المعرف الفريد لأمر الشراء</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>company_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NOT NULL, FOREIGN KEY</td>
                            <td>معرف الشركة</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>branch_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NOT NULL, FOREIGN KEY</td>
                            <td>معرف الفرع</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>supplier_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NOT NULL, FOREIGN KEY</td>
                            <td>معرف المورد</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>user_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NOT NULL, FOREIGN KEY</td>
                            <td>معرف المستخدم</td>
                        </tr>
                        <tr class="required">
                            <td>po_number</td>
                            <td>VARCHAR(50)</td>
                            <td>NOT NULL, UNIQUE</td>
                            <td>رقم أمر الشراء</td>
                        </tr>
                        <tr class="required">
                            <td>po_date</td>
                            <td>DATE</td>
                            <td>NOT NULL</td>
                            <td>تاريخ أمر الشراء</td>
                        </tr>
                        <tr class="optional">
                            <td>expected_delivery_date</td>
                            <td>DATE</td>
                            <td>NULLABLE</td>
                            <td>تاريخ التسليم المتوقع</td>
                        </tr>
                        <tr class="required">
                            <td>status</td>
                            <td>ENUM</td>
                            <td>('draft','sent','confirmed','partially_received','received','cancelled')</td>
                            <td>حالة أمر الشراء</td>
                        </tr>
                        <tr class="required">
                            <td>subtotal</td>
                            <td>DECIMAL(12,2)</td>
                            <td>DEFAULT 0.00</td>
                            <td>المجموع الفرعي</td>
                        </tr>
                        <tr class="required">
                            <td>discount_amount</td>
                            <td>DECIMAL(12,2)</td>
                            <td>DEFAULT 0.00</td>
                            <td>مبلغ الخصم</td>
                        </tr>
                        <tr class="required">
                            <td>tax_amount</td>
                            <td>DECIMAL(12,2)</td>
                            <td>DEFAULT 0.00</td>
                            <td>مبلغ الضريبة</td>
                        </tr>
                        <tr class="required">
                            <td>shipping_cost</td>
                            <td>DECIMAL(12,2)</td>
                            <td>DEFAULT 0.00</td>
                            <td>تكلفة الشحن</td>
                        </tr>
                        <tr class="required">
                            <td>total_amount</td>
                            <td>DECIMAL(12,2)</td>
                            <td>DEFAULT 0.00</td>
                            <td>المبلغ الإجمالي</td>
                        </tr>
                        <tr class="optional">
                            <td>terms_and_conditions</td>
                            <td>TEXT</td>
                            <td>NULLABLE</td>
                            <td>الشروط والأحكام</td>
                        </tr>
                        <tr class="optional">
                            <td>notes</td>
                            <td>TEXT</td>
                            <td>NULLABLE</td>
                            <td>ملاحظات</td>
                        </tr>
                        <tr class="required">
                            <td>created_at</td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP</td>
                            <td>تاريخ الإنشاء</td>
                        </tr>
                        <tr class="required">
                            <td>updated_at</td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP ON UPDATE</td>
                            <td>تاريخ التحديث</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="sql-code">
<span class="sql-comment">-- جدول أوامر الشراء</span>
<span class="sql-keyword">CREATE TABLE</span> purchase_orders (
    id <span class="sql-keyword">BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT</span>,
    company_id <span class="sql-keyword">BIGINT UNSIGNED NOT NULL</span>,
    branch_id <span class="sql-keyword">BIGINT UNSIGNED NOT NULL</span>,
    supplier_id <span class="sql-keyword">BIGINT UNSIGNED NOT NULL</span>,
    user_id <span class="sql-keyword">BIGINT UNSIGNED NOT NULL</span>,
    po_number <span class="sql-keyword">VARCHAR</span>(50) <span class="sql-keyword">NOT NULL UNIQUE</span>,
    po_date <span class="sql-keyword">DATE NOT NULL</span>,
    expected_delivery_date <span class="sql-keyword">DATE</span>,
    status <span class="sql-keyword">ENUM</span>(<span class="sql-string">'draft','sent','confirmed','partially_received','received','cancelled'</span>) <span class="sql-keyword">DEFAULT</span> <span class="sql-string">'draft'</span>,
    subtotal <span class="sql-keyword">DECIMAL</span>(12,2) <span class="sql-keyword">DEFAULT</span> 0.00,
    discount_amount <span class="sql-keyword">DECIMAL</span>(12,2) <span class="sql-keyword">DEFAULT</span> 0.00,
    tax_amount <span class="sql-keyword">DECIMAL</span>(12,2) <span class="sql-keyword">DEFAULT</span> 0.00,
    shipping_cost <span class="sql-keyword">DECIMAL</span>(12,2) <span class="sql-keyword">DEFAULT</span> 0.00,
    total_amount <span class="sql-keyword">DECIMAL</span>(12,2) <span class="sql-keyword">DEFAULT</span> 0.00,
    terms_and_conditions <span class="sql-keyword">TEXT</span>,
    notes <span class="sql-keyword">TEXT</span>,
    created_at <span class="sql-keyword">TIMESTAMP DEFAULT CURRENT_TIMESTAMP</span>,
    updated_at <span class="sql-keyword">TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (company_id) <span class="sql-keyword">REFERENCES</span> companies(id) <span class="sql-keyword">ON DELETE CASCADE</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (branch_id) <span class="sql-keyword">REFERENCES</span> branches(id) <span class="sql-keyword">ON DELETE CASCADE</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (supplier_id) <span class="sql-keyword">REFERENCES</span> suppliers(id) <span class="sql-keyword">ON DELETE CASCADE</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (user_id) <span class="sql-keyword">REFERENCES</span> users(id),
    <span class="sql-keyword">INDEX</span> idx_po_number (po_number),
    <span class="sql-keyword">INDEX</span> idx_po_date (po_date),
    <span class="sql-keyword">INDEX</span> idx_po_status (status),
    <span class="sql-keyword">INDEX</span> idx_supplier_orders (supplier_id)
);
            </div>

            <h3>7.3 جدول تفاصيل أوامر الشراء (purchase_order_items)</h3>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>اسم الحقل</th>
                            <th>نوع البيانات</th>
                            <th>القيود</th>
                            <th>الوصف</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="primary-key">
                            <td>id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>PRIMARY KEY, AUTO_INCREMENT</td>
                            <td>المعرف الفريد للصنف</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>purchase_order_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NOT NULL, FOREIGN KEY</td>
                            <td>معرف أمر الشراء</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>product_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NOT NULL, FOREIGN KEY</td>
                            <td>معرف المنتج</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>product_variant_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NULLABLE, FOREIGN KEY</td>
                            <td>معرف متغير المنتج</td>
                        </tr>
                        <tr class="required">
                            <td>quantity_ordered</td>
                            <td>DECIMAL(10,3)</td>
                            <td>NOT NULL</td>
                            <td>الكمية المطلوبة</td>
                        </tr>
                        <tr class="required">
                            <td>quantity_received</td>
                            <td>DECIMAL(10,3)</td>
                            <td>DEFAULT 0.000</td>
                            <td>الكمية المستلمة</td>
                        </tr>
                        <tr class="required">
                            <td>unit_price</td>
                            <td>DECIMAL(12,2)</td>
                            <td>NOT NULL</td>
                            <td>سعر الوحدة</td>
                        </tr>
                        <tr class="required">
                            <td>discount_percentage</td>
                            <td>DECIMAL(5,2)</td>
                            <td>DEFAULT 0.00</td>
                            <td>نسبة الخصم</td>
                        </tr>
                        <tr class="required">
                            <td>discount_amount</td>
                            <td>DECIMAL(12,2)</td>
                            <td>DEFAULT 0.00</td>
                            <td>مبلغ الخصم</td>
                        </tr>
                        <tr class="required">
                            <td>tax_percentage</td>
                            <td>DECIMAL(5,2)</td>
                            <td>DEFAULT 0.00</td>
                            <td>نسبة الضريبة</td>
                        </tr>
                        <tr class="required">
                            <td>tax_amount</td>
                            <td>DECIMAL(12,2)</td>
                            <td>DEFAULT 0.00</td>
                            <td>مبلغ الضريبة</td>
                        </tr>
                        <tr class="required">
                            <td>line_total</td>
                            <td>DECIMAL(12,2)</td>
                            <td>NOT NULL</td>
                            <td>إجمالي السطر</td>
                        </tr>
                        <tr class="optional">
                            <td>notes</td>
                            <td>TEXT</td>
                            <td>NULLABLE</td>
                            <td>ملاحظات</td>
                        </tr>
                        <tr class="required">
                            <td>created_at</td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP</td>
                            <td>تاريخ الإنشاء</td>
                        </tr>
                        <tr class="required">
                            <td>updated_at</td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP ON UPDATE</td>
                            <td>تاريخ التحديث</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="sql-code">
<span class="sql-comment">-- جدول تفاصيل أوامر الشراء</span>
<span class="sql-keyword">CREATE TABLE</span> purchase_order_items (
    id <span class="sql-keyword">BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT</span>,
    purchase_order_id <span class="sql-keyword">BIGINT UNSIGNED NOT NULL</span>,
    product_id <span class="sql-keyword">BIGINT UNSIGNED NOT NULL</span>,
    product_variant_id <span class="sql-keyword">BIGINT UNSIGNED</span>,
    quantity_ordered <span class="sql-keyword">DECIMAL</span>(10,3) <span class="sql-keyword">NOT NULL</span>,
    quantity_received <span class="sql-keyword">DECIMAL</span>(10,3) <span class="sql-keyword">DEFAULT</span> 0.000,
    unit_price <span class="sql-keyword">DECIMAL</span>(12,2) <span class="sql-keyword">NOT NULL</span>,
    discount_percentage <span class="sql-keyword">DECIMAL</span>(5,2) <span class="sql-keyword">DEFAULT</span> 0.00,
    discount_amount <span class="sql-keyword">DECIMAL</span>(12,2) <span class="sql-keyword">DEFAULT</span> 0.00,
    tax_percentage <span class="sql-keyword">DECIMAL</span>(5,2) <span class="sql-keyword">DEFAULT</span> 0.00,
    tax_amount <span class="sql-keyword">DECIMAL</span>(12,2) <span class="sql-keyword">DEFAULT</span> 0.00,
    line_total <span class="sql-keyword">DECIMAL</span>(12,2) <span class="sql-keyword">NOT NULL</span>,
    notes <span class="sql-keyword">TEXT</span>,
    created_at <span class="sql-keyword">TIMESTAMP DEFAULT CURRENT_TIMESTAMP</span>,
    updated_at <span class="sql-keyword">TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (purchase_order_id) <span class="sql-keyword">REFERENCES</span> purchase_orders(id) <span class="sql-keyword">ON DELETE CASCADE</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (product_id) <span class="sql-keyword">REFERENCES</span> products(id),
    <span class="sql-keyword">FOREIGN KEY</span> (product_variant_id) <span class="sql-keyword">REFERENCES</span> product_variants(id),
    <span class="sql-keyword">INDEX</span> idx_po_items (purchase_order_id),
    <span class="sql-keyword">INDEX</span> idx_product_purchases (product_id)
);
            </div>
        </div>
    </div>
</body>
</html>
