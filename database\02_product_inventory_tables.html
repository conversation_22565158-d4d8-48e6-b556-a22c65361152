<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>جداول المنتجات والمخزون - نظام ERP للخياطة</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            margin-top: 20px;
            margin-bottom: 20px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            padding: 40px 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 2.5em;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .section {
            margin-bottom: 50px;
            padding: 30px;
            background: #f8f9fa;
            border-radius: 15px;
            border: 1px solid #e9ecef;
        }
        
        .section h2 {
            color: #495057;
            font-size: 2em;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 3px solid #667eea;
        }
        
        .section h3 {
            color: #6c757d;
            font-size: 1.5em;
            margin: 25px 0 15px 0;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border-left: 4px solid #28a745;
        }
        
        .table-container {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin: 20px 0;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            padding: 12px 15px;
            text-align: right;
            border-bottom: 1px solid #dee2e6;
            font-size: 0.9em;
        }
        
        th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: 600;
            position: sticky;
            top: 0;
        }
        
        tr:hover {
            background: #f8f9fa;
        }
        
        .primary-key {
            background: #fff3cd;
            font-weight: bold;
        }
        
        .foreign-key {
            background: #d1ecf1;
        }
        
        .required {
            color: #dc3545;
            font-weight: bold;
        }
        
        .optional {
            color: #6c757d;
        }
        
        .sql-code {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.85em;
            overflow-x: auto;
            margin: 20px 0;
            white-space: pre-wrap;
        }
        
        .sql-keyword {
            color: #63b3ed;
            font-weight: bold;
        }
        
        .sql-string {
            color: #68d391;
        }
        
        .sql-comment {
            color: #a0aec0;
            font-style: italic;
        }
        
        .info-box {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-left: 5px solid #17a2b8;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .nav-links {
            background: #e9ecef;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .nav-links a {
            display: inline-block;
            margin: 5px 10px;
            padding: 10px 20px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-weight: 500;
        }
        
        .nav-links a:hover {
            background: #5a67d8;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>جداول المنتجات والمخزون</h1>
            <p>نظام ERP لمعامل ومحلات الخياطة</p>
        </div>

        <div class="nav-links">
            <a href="../index.html">الصفحة الرئيسية</a>
            <a href="01_core_tables.html">الجداول الأساسية</a>
            <a href="02_product_inventory_tables.html">المنتجات والمخزون</a>
            <a href="03_sales_pos_tables.html">المبيعات ونقطة البيع</a>
            <a href="04_accounting_tables.html">المحاسبة</a>
            <a href="05_manufacturing_tables.html">التصنيع</a>
            <a href="06_crm_tables.html">إدارة العملاء</a>
            <a href="07_purchasing_tables.html">المشتريات والموردين</a>
            <a href="08_system_tables.html">جداول النظام</a>
        </div>

        <div class="section">
            <h2>2. جداول المنتجات والمخزون</h2>
            
            <div class="info-box">
                <h4>نظرة عامة:</h4>
                <p>هذه الجداول تدير جميع المنتجات والمواد الخام والمخزون عبر الفروع المختلفة، مع دعم المتغيرات والباركود وتتبع الحركات.</p>
            </div>

            <h3>2.1 جدول فئات المنتجات (product_categories)</h3>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>اسم الحقل</th>
                            <th>نوع البيانات</th>
                            <th>القيود</th>
                            <th>الوصف</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="primary-key">
                            <td>id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>PRIMARY KEY, AUTO_INCREMENT</td>
                            <td>المعرف الفريد للفئة</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>company_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NOT NULL, FOREIGN KEY</td>
                            <td>معرف الشركة</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>parent_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NULLABLE, FOREIGN KEY</td>
                            <td>معرف الفئة الأب</td>
                        </tr>
                        <tr class="required">
                            <td>name</td>
                            <td>VARCHAR(255)</td>
                            <td>NOT NULL</td>
                            <td>اسم الفئة</td>
                        </tr>
                        <tr class="required">
                            <td>code</td>
                            <td>VARCHAR(50)</td>
                            <td>NOT NULL</td>
                            <td>رمز الفئة</td>
                        </tr>
                        <tr class="optional">
                            <td>description</td>
                            <td>TEXT</td>
                            <td>NULLABLE</td>
                            <td>وصف الفئة</td>
                        </tr>
                        <tr class="optional">
                            <td>image</td>
                            <td>VARCHAR(255)</td>
                            <td>NULLABLE</td>
                            <td>صورة الفئة</td>
                        </tr>
                        <tr class="required">
                            <td>sort_order</td>
                            <td>INT</td>
                            <td>DEFAULT 0</td>
                            <td>ترتيب العرض</td>
                        </tr>
                        <tr class="required">
                            <td>is_active</td>
                            <td>BOOLEAN</td>
                            <td>DEFAULT TRUE</td>
                            <td>حالة النشاط</td>
                        </tr>
                        <tr class="required">
                            <td>created_at</td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP</td>
                            <td>تاريخ الإنشاء</td>
                        </tr>
                        <tr class="required">
                            <td>updated_at</td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP ON UPDATE</td>
                            <td>تاريخ التحديث</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="sql-code">
<span class="sql-comment">-- جدول فئات المنتجات</span>
<span class="sql-keyword">CREATE TABLE</span> product_categories (
    id <span class="sql-keyword">BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT</span>,
    company_id <span class="sql-keyword">BIGINT UNSIGNED NOT NULL</span>,
    parent_id <span class="sql-keyword">BIGINT UNSIGNED</span>,
    name <span class="sql-keyword">VARCHAR</span>(255) <span class="sql-keyword">NOT NULL</span>,
    code <span class="sql-keyword">VARCHAR</span>(50) <span class="sql-keyword">NOT NULL</span>,
    description <span class="sql-keyword">TEXT</span>,
    image <span class="sql-keyword">VARCHAR</span>(255),
    sort_order <span class="sql-keyword">INT DEFAULT</span> 0,
    is_active <span class="sql-keyword">BOOLEAN DEFAULT TRUE</span>,
    created_at <span class="sql-keyword">TIMESTAMP DEFAULT CURRENT_TIMESTAMP</span>,
    updated_at <span class="sql-keyword">TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (company_id) <span class="sql-keyword">REFERENCES</span> companies(id) <span class="sql-keyword">ON DELETE CASCADE</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (parent_id) <span class="sql-keyword">REFERENCES</span> product_categories(id) <span class="sql-keyword">ON DELETE SET NULL</span>,
    <span class="sql-keyword">UNIQUE KEY</span> unique_category_code (company_id, code)
);
            </div>
            <h3>2.2 جدول وحدات القياس (units_of_measure)</h3>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>اسم الحقل</th>
                            <th>نوع البيانات</th>
                            <th>القيود</th>
                            <th>الوصف</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="primary-key">
                            <td>id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>PRIMARY KEY, AUTO_INCREMENT</td>
                            <td>المعرف الفريد للوحدة</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>company_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NOT NULL, FOREIGN KEY</td>
                            <td>معرف الشركة</td>
                        </tr>
                        <tr class="required">
                            <td>name</td>
                            <td>VARCHAR(100)</td>
                            <td>NOT NULL</td>
                            <td>اسم الوحدة</td>
                        </tr>
                        <tr class="required">
                            <td>symbol</td>
                            <td>VARCHAR(10)</td>
                            <td>NOT NULL</td>
                            <td>رمز الوحدة</td>
                        </tr>
                        <tr class="required">
                            <td>type</td>
                            <td>ENUM</td>
                            <td>('length','weight','area','volume','piece')</td>
                            <td>نوع الوحدة</td>
                        </tr>
                        <tr class="required">
                            <td>is_base_unit</td>
                            <td>BOOLEAN</td>
                            <td>DEFAULT FALSE</td>
                            <td>هل هي الوحدة الأساسية</td>
                        </tr>
                        <tr class="optional">
                            <td>conversion_factor</td>
                            <td>DECIMAL(10,4)</td>
                            <td>DEFAULT 1.0000</td>
                            <td>معامل التحويل للوحدة الأساسية</td>
                        </tr>
                        <tr class="required">
                            <td>is_active</td>
                            <td>BOOLEAN</td>
                            <td>DEFAULT TRUE</td>
                            <td>حالة النشاط</td>
                        </tr>
                        <tr class="required">
                            <td>created_at</td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP</td>
                            <td>تاريخ الإنشاء</td>
                        </tr>
                        <tr class="required">
                            <td>updated_at</td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP ON UPDATE</td>
                            <td>تاريخ التحديث</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="sql-code">
<span class="sql-comment">-- جدول وحدات القياس</span>
<span class="sql-keyword">CREATE TABLE</span> units_of_measure (
    id <span class="sql-keyword">BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT</span>,
    company_id <span class="sql-keyword">BIGINT UNSIGNED NOT NULL</span>,
    name <span class="sql-keyword">VARCHAR</span>(100) <span class="sql-keyword">NOT NULL</span>,
    symbol <span class="sql-keyword">VARCHAR</span>(10) <span class="sql-keyword">NOT NULL</span>,
    type <span class="sql-keyword">ENUM</span>(<span class="sql-string">'length','weight','area','volume','piece'</span>) <span class="sql-keyword">NOT NULL</span>,
    is_base_unit <span class="sql-keyword">BOOLEAN DEFAULT FALSE</span>,
    conversion_factor <span class="sql-keyword">DECIMAL</span>(10,4) <span class="sql-keyword">DEFAULT</span> 1.0000,
    is_active <span class="sql-keyword">BOOLEAN DEFAULT TRUE</span>,
    created_at <span class="sql-keyword">TIMESTAMP DEFAULT CURRENT_TIMESTAMP</span>,
    updated_at <span class="sql-keyword">TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (company_id) <span class="sql-keyword">REFERENCES</span> companies(id) <span class="sql-keyword">ON DELETE CASCADE</span>,
    <span class="sql-keyword">UNIQUE KEY</span> unique_unit_symbol (company_id, symbol)
);
            </div>

            <h3>2.3 جدول المنتجات (products)</h3>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>اسم الحقل</th>
                            <th>نوع البيانات</th>
                            <th>القيود</th>
                            <th>الوصف</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="primary-key">
                            <td>id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>PRIMARY KEY, AUTO_INCREMENT</td>
                            <td>المعرف الفريد للمنتج</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>company_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NOT NULL, FOREIGN KEY</td>
                            <td>معرف الشركة</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>category_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NOT NULL, FOREIGN KEY</td>
                            <td>معرف الفئة</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>unit_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NOT NULL, FOREIGN KEY</td>
                            <td>معرف وحدة القياس</td>
                        </tr>
                        <tr class="required">
                            <td>name</td>
                            <td>VARCHAR(255)</td>
                            <td>NOT NULL</td>
                            <td>اسم المنتج</td>
                        </tr>
                        <tr class="required">
                            <td>sku</td>
                            <td>VARCHAR(100)</td>
                            <td>NOT NULL, UNIQUE</td>
                            <td>رمز المنتج</td>
                        </tr>
                        <tr class="optional">
                            <td>barcode</td>
                            <td>VARCHAR(100)</td>
                            <td>NULLABLE, UNIQUE</td>
                            <td>الباركود</td>
                        </tr>
                        <tr class="optional">
                            <td>description</td>
                            <td>TEXT</td>
                            <td>NULLABLE</td>
                            <td>وصف المنتج</td>
                        </tr>
                        <tr class="required">
                            <td>product_type</td>
                            <td>ENUM</td>
                            <td>('finished','raw_material','semi_finished','service')</td>
                            <td>نوع المنتج</td>
                        </tr>
                        <tr class="required">
                            <td>item_type</td>
                            <td>ENUM</td>
                            <td>('stockable','consumable','service')</td>
                            <td>نوع الصنف</td>
                        </tr>
                        <tr class="required">
                            <td>cost_price</td>
                            <td>DECIMAL(12,2)</td>
                            <td>DEFAULT 0.00</td>
                            <td>سعر التكلفة</td>
                        </tr>
                        <tr class="required">
                            <td>selling_price</td>
                            <td>DECIMAL(12,2)</td>
                            <td>DEFAULT 0.00</td>
                            <td>سعر البيع</td>
                        </tr>
                        <tr class="optional">
                            <td>wholesale_price</td>
                            <td>DECIMAL(12,2)</td>
                            <td>DEFAULT 0.00</td>
                            <td>سعر الجملة</td>
                        </tr>
                        <tr class="required">
                            <td>has_variants</td>
                            <td>BOOLEAN</td>
                            <td>DEFAULT FALSE</td>
                            <td>له متغيرات</td>
                        </tr>
                        <tr class="required">
                            <td>track_inventory</td>
                            <td>BOOLEAN</td>
                            <td>DEFAULT TRUE</td>
                            <td>تتبع المخزون</td>
                        </tr>
                        <tr class="optional">
                            <td>min_stock_level</td>
                            <td>DECIMAL(10,2)</td>
                            <td>DEFAULT 0.00</td>
                            <td>الحد الأدنى للمخزون</td>
                        </tr>
                        <tr class="optional">
                            <td>max_stock_level</td>
                            <td>DECIMAL(10,2)</td>
                            <td>DEFAULT 0.00</td>
                            <td>الحد الأقصى للمخزون</td>
                        </tr>
                        <tr class="optional">
                            <td>reorder_level</td>
                            <td>DECIMAL(10,2)</td>
                            <td>DEFAULT 0.00</td>
                            <td>مستوى إعادة الطلب</td>
                        </tr>
                        <tr class="optional">
                            <td>weight</td>
                            <td>DECIMAL(8,3)</td>
                            <td>NULLABLE</td>
                            <td>الوزن</td>
                        </tr>
                        <tr class="optional">
                            <td>dimensions</td>
                            <td>VARCHAR(100)</td>
                            <td>NULLABLE</td>
                            <td>الأبعاد</td>
                        </tr>
                        <tr class="optional">
                            <td>image</td>
                            <td>VARCHAR(255)</td>
                            <td>NULLABLE</td>
                            <td>صورة المنتج</td>
                        </tr>
                        <tr class="optional">
                            <td>notes</td>
                            <td>TEXT</td>
                            <td>NULLABLE</td>
                            <td>ملاحظات</td>
                        </tr>
                        <tr class="required">
                            <td>is_active</td>
                            <td>BOOLEAN</td>
                            <td>DEFAULT TRUE</td>
                            <td>حالة النشاط</td>
                        </tr>
                        <tr class="required">
                            <td>created_at</td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP</td>
                            <td>تاريخ الإنشاء</td>
                        </tr>
                        <tr class="required">
                            <td>updated_at</td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP ON UPDATE</td>
                            <td>تاريخ التحديث</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="sql-code">
<span class="sql-comment">-- جدول المنتجات</span>
<span class="sql-keyword">CREATE TABLE</span> products (
    id <span class="sql-keyword">BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT</span>,
    company_id <span class="sql-keyword">BIGINT UNSIGNED NOT NULL</span>,
    category_id <span class="sql-keyword">BIGINT UNSIGNED NOT NULL</span>,
    unit_id <span class="sql-keyword">BIGINT UNSIGNED NOT NULL</span>,
    name <span class="sql-keyword">VARCHAR</span>(255) <span class="sql-keyword">NOT NULL</span>,
    sku <span class="sql-keyword">VARCHAR</span>(100) <span class="sql-keyword">NOT NULL UNIQUE</span>,
    barcode <span class="sql-keyword">VARCHAR</span>(100) <span class="sql-keyword">UNIQUE</span>,
    description <span class="sql-keyword">TEXT</span>,
    product_type <span class="sql-keyword">ENUM</span>(<span class="sql-string">'finished','raw_material','semi_finished','service'</span>) <span class="sql-keyword">NOT NULL</span>,
    item_type <span class="sql-keyword">ENUM</span>(<span class="sql-string">'stockable','consumable','service'</span>) <span class="sql-keyword">DEFAULT</span> <span class="sql-string">'stockable'</span>,
    cost_price <span class="sql-keyword">DECIMAL</span>(12,2) <span class="sql-keyword">DEFAULT</span> 0.00,
    selling_price <span class="sql-keyword">DECIMAL</span>(12,2) <span class="sql-keyword">DEFAULT</span> 0.00,
    wholesale_price <span class="sql-keyword">DECIMAL</span>(12,2) <span class="sql-keyword">DEFAULT</span> 0.00,
    has_variants <span class="sql-keyword">BOOLEAN DEFAULT FALSE</span>,
    track_inventory <span class="sql-keyword">BOOLEAN DEFAULT TRUE</span>,
    min_stock_level <span class="sql-keyword">DECIMAL</span>(10,2) <span class="sql-keyword">DEFAULT</span> 0.00,
    max_stock_level <span class="sql-keyword">DECIMAL</span>(10,2) <span class="sql-keyword">DEFAULT</span> 0.00,
    reorder_level <span class="sql-keyword">DECIMAL</span>(10,2) <span class="sql-keyword">DEFAULT</span> 0.00,
    weight <span class="sql-keyword">DECIMAL</span>(8,3),
    dimensions <span class="sql-keyword">VARCHAR</span>(100),
    image <span class="sql-keyword">VARCHAR</span>(255),
    notes <span class="sql-keyword">TEXT</span>,
    is_active <span class="sql-keyword">BOOLEAN DEFAULT TRUE</span>,
    created_at <span class="sql-keyword">TIMESTAMP DEFAULT CURRENT_TIMESTAMP</span>,
    updated_at <span class="sql-keyword">TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (company_id) <span class="sql-keyword">REFERENCES</span> companies(id) <span class="sql-keyword">ON DELETE CASCADE</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (category_id) <span class="sql-keyword">REFERENCES</span> product_categories(id),
    <span class="sql-keyword">FOREIGN KEY</span> (unit_id) <span class="sql-keyword">REFERENCES</span> units_of_measure(id),
    <span class="sql-keyword">INDEX</span> idx_product_sku (sku),
    <span class="sql-keyword">INDEX</span> idx_product_barcode (barcode),
    <span class="sql-keyword">INDEX</span> idx_product_type (product_type)
);
            </div>

            <h3>2.4 جدول متغيرات المنتجات (product_variants)</h3>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>اسم الحقل</th>
                            <th>نوع البيانات</th>
                            <th>القيود</th>
                            <th>الوصف</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="primary-key">
                            <td>id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>PRIMARY KEY, AUTO_INCREMENT</td>
                            <td>المعرف الفريد للمتغير</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>product_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NOT NULL, FOREIGN KEY</td>
                            <td>معرف المنتج الأساسي</td>
                        </tr>
                        <tr class="required">
                            <td>variant_name</td>
                            <td>VARCHAR(255)</td>
                            <td>NOT NULL</td>
                            <td>اسم المتغير</td>
                        </tr>
                        <tr class="required">
                            <td>sku</td>
                            <td>VARCHAR(100)</td>
                            <td>NOT NULL, UNIQUE</td>
                            <td>رمز المتغير</td>
                        </tr>
                        <tr class="optional">
                            <td>barcode</td>
                            <td>VARCHAR(100)</td>
                            <td>NULLABLE, UNIQUE</td>
                            <td>باركود المتغير</td>
                        </tr>
                        <tr class="optional">
                            <td>color</td>
                            <td>VARCHAR(50)</td>
                            <td>NULLABLE</td>
                            <td>اللون</td>
                        </tr>
                        <tr class="optional">
                            <td>size</td>
                            <td>VARCHAR(50)</td>
                            <td>NULLABLE</td>
                            <td>المقاس</td>
                        </tr>
                        <tr class="optional">
                            <td>material</td>
                            <td>VARCHAR(100)</td>
                            <td>NULLABLE</td>
                            <td>نوع القماش</td>
                        </tr>
                        <tr class="optional">
                            <td>style</td>
                            <td>VARCHAR(100)</td>
                            <td>NULLABLE</td>
                            <td>الطراز</td>
                        </tr>
                        <tr class="required">
                            <td>cost_price</td>
                            <td>DECIMAL(12,2)</td>
                            <td>DEFAULT 0.00</td>
                            <td>سعر التكلفة</td>
                        </tr>
                        <tr class="required">
                            <td>selling_price</td>
                            <td>DECIMAL(12,2)</td>
                            <td>DEFAULT 0.00</td>
                            <td>سعر البيع</td>
                        </tr>
                        <tr class="optional">
                            <td>wholesale_price</td>
                            <td>DECIMAL(12,2)</td>
                            <td>DEFAULT 0.00</td>
                            <td>سعر الجملة</td>
                        </tr>
                        <tr class="optional">
                            <td>weight</td>
                            <td>DECIMAL(8,3)</td>
                            <td>NULLABLE</td>
                            <td>الوزن</td>
                        </tr>
                        <tr class="optional">
                            <td>image</td>
                            <td>VARCHAR(255)</td>
                            <td>NULLABLE</td>
                            <td>صورة المتغير</td>
                        </tr>
                        <tr class="required">
                            <td>is_active</td>
                            <td>BOOLEAN</td>
                            <td>DEFAULT TRUE</td>
                            <td>حالة النشاط</td>
                        </tr>
                        <tr class="required">
                            <td>created_at</td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP</td>
                            <td>تاريخ الإنشاء</td>
                        </tr>
                        <tr class="required">
                            <td>updated_at</td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP ON UPDATE</td>
                            <td>تاريخ التحديث</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="sql-code">
<span class="sql-comment">-- جدول متغيرات المنتجات</span>
<span class="sql-keyword">CREATE TABLE</span> product_variants (
    id <span class="sql-keyword">BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT</span>,
    product_id <span class="sql-keyword">BIGINT UNSIGNED NOT NULL</span>,
    variant_name <span class="sql-keyword">VARCHAR</span>(255) <span class="sql-keyword">NOT NULL</span>,
    sku <span class="sql-keyword">VARCHAR</span>(100) <span class="sql-keyword">NOT NULL UNIQUE</span>,
    barcode <span class="sql-keyword">VARCHAR</span>(100) <span class="sql-keyword">UNIQUE</span>,
    color <span class="sql-keyword">VARCHAR</span>(50),
    size <span class="sql-keyword">VARCHAR</span>(50),
    material <span class="sql-keyword">VARCHAR</span>(100),
    style <span class="sql-keyword">VARCHAR</span>(100),
    cost_price <span class="sql-keyword">DECIMAL</span>(12,2) <span class="sql-keyword">DEFAULT</span> 0.00,
    selling_price <span class="sql-keyword">DECIMAL</span>(12,2) <span class="sql-keyword">DEFAULT</span> 0.00,
    wholesale_price <span class="sql-keyword">DECIMAL</span>(12,2) <span class="sql-keyword">DEFAULT</span> 0.00,
    weight <span class="sql-keyword">DECIMAL</span>(8,3),
    image <span class="sql-keyword">VARCHAR</span>(255),
    is_active <span class="sql-keyword">BOOLEAN DEFAULT TRUE</span>,
    created_at <span class="sql-keyword">TIMESTAMP DEFAULT CURRENT_TIMESTAMP</span>,
    updated_at <span class="sql-keyword">TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (product_id) <span class="sql-keyword">REFERENCES</span> products(id) <span class="sql-keyword">ON DELETE CASCADE</span>,
    <span class="sql-keyword">INDEX</span> idx_variant_sku (sku),
    <span class="sql-keyword">INDEX</span> idx_variant_barcode (barcode),
    <span class="sql-keyword">INDEX</span> idx_variant_color (color),
    <span class="sql-keyword">INDEX</span> idx_variant_size (size)
);
            </div>

            <h3>2.5 جدول مواقع المخزون (inventory_locations)</h3>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>اسم الحقل</th>
                            <th>نوع البيانات</th>
                            <th>القيود</th>
                            <th>الوصف</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="primary-key">
                            <td>id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>PRIMARY KEY, AUTO_INCREMENT</td>
                            <td>المعرف الفريد للموقع</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>company_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NOT NULL, FOREIGN KEY</td>
                            <td>معرف الشركة</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>branch_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NOT NULL, FOREIGN KEY</td>
                            <td>معرف الفرع</td>
                        </tr>
                        <tr class="required">
                            <td>location_code</td>
                            <td>VARCHAR(50)</td>
                            <td>NOT NULL</td>
                            <td>رمز الموقع</td>
                        </tr>
                        <tr class="required">
                            <td>location_name</td>
                            <td>VARCHAR(255)</td>
                            <td>NOT NULL</td>
                            <td>اسم الموقع</td>
                        </tr>
                        <tr class="required">
                            <td>location_type</td>
                            <td>ENUM</td>
                            <td>('warehouse','showroom','production','quality_control','damaged','transit')</td>
                            <td>نوع الموقع</td>
                        </tr>
                        <tr class="optional">
                            <td>description</td>
                            <td>TEXT</td>
                            <td>NULLABLE</td>
                            <td>وصف الموقع</td>
                        </tr>
                        <tr class="required">
                            <td>is_default</td>
                            <td>BOOLEAN</td>
                            <td>DEFAULT FALSE</td>
                            <td>الموقع الافتراضي</td>
                        </tr>
                        <tr class="required">
                            <td>is_active</td>
                            <td>BOOLEAN</td>
                            <td>DEFAULT TRUE</td>
                            <td>حالة النشاط</td>
                        </tr>
                        <tr class="required">
                            <td>created_at</td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP</td>
                            <td>تاريخ الإنشاء</td>
                        </tr>
                        <tr class="required">
                            <td>updated_at</td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP ON UPDATE</td>
                            <td>تاريخ التحديث</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="sql-code">
<span class="sql-comment">-- جدول مواقع المخزون</span>
<span class="sql-keyword">CREATE TABLE</span> inventory_locations (
    id <span class="sql-keyword">BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT</span>,
    company_id <span class="sql-keyword">BIGINT UNSIGNED NOT NULL</span>,
    branch_id <span class="sql-keyword">BIGINT UNSIGNED NOT NULL</span>,
    location_code <span class="sql-keyword">VARCHAR</span>(50) <span class="sql-keyword">NOT NULL</span>,
    location_name <span class="sql-keyword">VARCHAR</span>(255) <span class="sql-keyword">NOT NULL</span>,
    location_type <span class="sql-keyword">ENUM</span>(<span class="sql-string">'warehouse','showroom','production','quality_control','damaged','transit'</span>) <span class="sql-keyword">DEFAULT</span> <span class="sql-string">'warehouse'</span>,
    description <span class="sql-keyword">TEXT</span>,
    is_default <span class="sql-keyword">BOOLEAN DEFAULT FALSE</span>,
    is_active <span class="sql-keyword">BOOLEAN DEFAULT TRUE</span>,
    created_at <span class="sql-keyword">TIMESTAMP DEFAULT CURRENT_TIMESTAMP</span>,
    updated_at <span class="sql-keyword">TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (company_id) <span class="sql-keyword">REFERENCES</span> companies(id) <span class="sql-keyword">ON DELETE CASCADE</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (branch_id) <span class="sql-keyword">REFERENCES</span> branches(id) <span class="sql-keyword">ON DELETE CASCADE</span>,
    <span class="sql-keyword">UNIQUE KEY</span> unique_location_code (company_id, location_code),
    <span class="sql-keyword">INDEX</span> idx_location_type (location_type),
    <span class="sql-keyword">INDEX</span> idx_location_branch (branch_id)
);
            </div>
            <h3>2.6 جدول المستودعات (warehouses)</h3>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>اسم الحقل</th>
                            <th>نوع البيانات</th>
                            <th>القيود</th>
                            <th>الوصف</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="primary-key">
                            <td>id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>PRIMARY KEY, AUTO_INCREMENT</td>
                            <td>المعرف الفريد للمستودع</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>company_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NOT NULL, FOREIGN KEY</td>
                            <td>معرف الشركة</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>branch_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NULLABLE, FOREIGN KEY</td>
                            <td>معرف الفرع</td>
                        </tr>
                        <tr class="required">
                            <td>warehouse_code</td>
                            <td>VARCHAR(50)</td>
                            <td>NOT NULL</td>
                            <td>رمز المستودع</td>
                        </tr>
                        <tr class="required">
                            <td>warehouse_name</td>
                            <td>VARCHAR(255)</td>
                            <td>NOT NULL</td>
                            <td>اسم المستودع</td>
                        </tr>
                        <tr class="required">
                            <td>warehouse_type</td>
                            <td>ENUM</td>
                            <td>('main','branch','transit','quality','damaged','virtual')</td>
                            <td>نوع المستودع</td>
                        </tr>
                        <tr class="optional">
                            <td>address</td>
                            <td>TEXT</td>
                            <td>NULLABLE</td>
                            <td>عنوان المستودع</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>manager_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NULLABLE, FOREIGN KEY</td>
                            <td>معرف مدير المستودع</td>
                        </tr>
                        <tr class="required">
                            <td>is_group</td>
                            <td>BOOLEAN</td>
                            <td>DEFAULT FALSE</td>
                            <td>مستودع مجموعة</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>parent_warehouse_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NULLABLE, FOREIGN KEY</td>
                            <td>معرف المستودع الأب</td>
                        </tr>
                        <tr class="required">
                            <td>is_active</td>
                            <td>BOOLEAN</td>
                            <td>DEFAULT TRUE</td>
                            <td>حالة النشاط</td>
                        </tr>
                        <tr class="required">
                            <td>created_at</td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP</td>
                            <td>تاريخ الإنشاء</td>
                        </tr>
                        <tr class="required">
                            <td>updated_at</td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP ON UPDATE</td>
                            <td>تاريخ التحديث</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="sql-code">
<span class="sql-comment">-- جدول المستودعات</span>
<span class="sql-keyword">CREATE TABLE</span> warehouses (
    id <span class="sql-keyword">BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT</span>,
    company_id <span class="sql-keyword">BIGINT UNSIGNED NOT NULL</span>,
    branch_id <span class="sql-keyword">BIGINT UNSIGNED</span>,
    warehouse_code <span class="sql-keyword">VARCHAR</span>(50) <span class="sql-keyword">NOT NULL</span>,
    warehouse_name <span class="sql-keyword">VARCHAR</span>(255) <span class="sql-keyword">NOT NULL</span>,
    warehouse_type <span class="sql-keyword">ENUM</span>(<span class="sql-string">'main','branch','transit','quality','damaged','virtual'</span>) <span class="sql-keyword">DEFAULT</span> <span class="sql-string">'main'</span>,
    address <span class="sql-keyword">TEXT</span>,
    manager_id <span class="sql-keyword">BIGINT UNSIGNED</span>,
    is_group <span class="sql-keyword">BOOLEAN DEFAULT FALSE</span>,
    parent_warehouse_id <span class="sql-keyword">BIGINT UNSIGNED</span>,
    is_active <span class="sql-keyword">BOOLEAN DEFAULT TRUE</span>,
    created_at <span class="sql-keyword">TIMESTAMP DEFAULT CURRENT_TIMESTAMP</span>,
    updated_at <span class="sql-keyword">TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (company_id) <span class="sql-keyword">REFERENCES</span> companies(id) <span class="sql-keyword">ON DELETE CASCADE</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (branch_id) <span class="sql-keyword">REFERENCES</span> branches(id) <span class="sql-keyword">ON DELETE SET NULL</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (manager_id) <span class="sql-keyword">REFERENCES</span> users(id) <span class="sql-keyword">ON DELETE SET NULL</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (parent_warehouse_id) <span class="sql-keyword">REFERENCES</span> warehouses(id) <span class="sql-keyword">ON DELETE SET NULL</span>,
    <span class="sql-keyword">UNIQUE KEY</span> unique_warehouse_code (company_id, warehouse_code),
    <span class="sql-keyword">INDEX</span> idx_warehouse_type (warehouse_type),
    <span class="sql-keyword">INDEX</span> idx_warehouse_active (is_active)
);
            </div>

            <h3>2.7 جدول قوائم الأسعار (price_lists)</h3>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>اسم الحقل</th>
                            <th>نوع البيانات</th>
                            <th>القيود</th>
                            <th>الوصف</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="primary-key">
                            <td>id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>PRIMARY KEY, AUTO_INCREMENT</td>
                            <td>المعرف الفريد لقائمة الأسعار</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>company_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NOT NULL, FOREIGN KEY</td>
                            <td>معرف الشركة</td>
                        </tr>
                        <tr class="required">
                            <td>price_list_name</td>
                            <td>VARCHAR(255)</td>
                            <td>NOT NULL</td>
                            <td>اسم قائمة الأسعار</td>
                        </tr>
                        <tr class="required">
                            <td>currency</td>
                            <td>VARCHAR(3)</td>
                            <td>NOT NULL</td>
                            <td>العملة</td>
                        </tr>
                        <tr class="required">
                            <td>buying</td>
                            <td>BOOLEAN</td>
                            <td>DEFAULT FALSE</td>
                            <td>قائمة أسعار شراء</td>
                        </tr>
                        <tr class="required">
                            <td>selling</td>
                            <td>BOOLEAN</td>
                            <td>DEFAULT TRUE</td>
                            <td>قائمة أسعار بيع</td>
                        </tr>
                        <tr class="optional">
                            <td>valid_from</td>
                            <td>DATE</td>
                            <td>NULLABLE</td>
                            <td>صالحة من تاريخ</td>
                        </tr>
                        <tr class="optional">
                            <td>valid_upto</td>
                            <td>DATE</td>
                            <td>NULLABLE</td>
                            <td>صالحة حتى تاريخ</td>
                        </tr>
                        <tr class="required">
                            <td>is_active</td>
                            <td>BOOLEAN</td>
                            <td>DEFAULT TRUE</td>
                            <td>حالة النشاط</td>
                        </tr>
                        <tr class="required">
                            <td>created_at</td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP</td>
                            <td>تاريخ الإنشاء</td>
                        </tr>
                        <tr class="required">
                            <td>updated_at</td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP ON UPDATE</td>
                            <td>تاريخ التحديث</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="sql-code">
<span class="sql-comment">-- جدول قوائم الأسعار</span>
<span class="sql-keyword">CREATE TABLE</span> price_lists (
    id <span class="sql-keyword">BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT</span>,
    company_id <span class="sql-keyword">BIGINT UNSIGNED NOT NULL</span>,
    price_list_name <span class="sql-keyword">VARCHAR</span>(255) <span class="sql-keyword">NOT NULL</span>,
    currency <span class="sql-keyword">VARCHAR</span>(3) <span class="sql-keyword">NOT NULL</span>,
    buying <span class="sql-keyword">BOOLEAN DEFAULT FALSE</span>,
    selling <span class="sql-keyword">BOOLEAN DEFAULT TRUE</span>,
    valid_from <span class="sql-keyword">DATE</span>,
    valid_upto <span class="sql-keyword">DATE</span>,
    is_active <span class="sql-keyword">BOOLEAN DEFAULT TRUE</span>,
    created_at <span class="sql-keyword">TIMESTAMP DEFAULT CURRENT_TIMESTAMP</span>,
    updated_at <span class="sql-keyword">TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (company_id) <span class="sql-keyword">REFERENCES</span> companies(id) <span class="sql-keyword">ON DELETE CASCADE</span>,
    <span class="sql-keyword">UNIQUE KEY</span> unique_price_list_name (company_id, price_list_name),
    <span class="sql-keyword">INDEX</span> idx_price_list_currency (currency),
    <span class="sql-keyword">INDEX</span> idx_price_list_dates (valid_from, valid_upto)
);
            </div>

            <h3>2.8 جدول أسعار الأصناف (item_prices)</h3>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>اسم الحقل</th>
                            <th>نوع البيانات</th>
                            <th>القيود</th>
                            <th>الوصف</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="primary-key">
                            <td>id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>PRIMARY KEY, AUTO_INCREMENT</td>
                            <td>المعرف الفريد للسعر</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>price_list_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NOT NULL, FOREIGN KEY</td>
                            <td>معرف قائمة الأسعار</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>product_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NOT NULL, FOREIGN KEY</td>
                            <td>معرف المنتج</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>product_variant_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NULLABLE, FOREIGN KEY</td>
                            <td>معرف متغير المنتج</td>
                        </tr>
                        <tr class="required">
                            <td>price</td>
                            <td>DECIMAL(12,2)</td>
                            <td>NOT NULL</td>
                            <td>السعر</td>
                        </tr>
                        <tr class="optional">
                            <td>valid_from</td>
                            <td>DATE</td>
                            <td>NULLABLE</td>
                            <td>صالح من تاريخ</td>
                        </tr>
                        <tr class="optional">
                            <td>valid_upto</td>
                            <td>DATE</td>
                            <td>NULLABLE</td>
                            <td>صالح حتى تاريخ</td>
                        </tr>
                        <tr class="required">
                            <td>created_at</td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP</td>
                            <td>تاريخ الإنشاء</td>
                        </tr>
                        <tr class="required">
                            <td>updated_at</td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP ON UPDATE</td>
                            <td>تاريخ التحديث</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="sql-code">
<span class="sql-comment">-- جدول أسعار الأصناف</span>
<span class="sql-keyword">CREATE TABLE</span> item_prices (
    id <span class="sql-keyword">BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT</span>,
    price_list_id <span class="sql-keyword">BIGINT UNSIGNED NOT NULL</span>,
    product_id <span class="sql-keyword">BIGINT UNSIGNED NOT NULL</span>,
    product_variant_id <span class="sql-keyword">BIGINT UNSIGNED</span>,
    price <span class="sql-keyword">DECIMAL</span>(12,2) <span class="sql-keyword">NOT NULL</span>,
    valid_from <span class="sql-keyword">DATE</span>,
    valid_upto <span class="sql-keyword">DATE</span>,
    created_at <span class="sql-keyword">TIMESTAMP DEFAULT CURRENT_TIMESTAMP</span>,
    updated_at <span class="sql-keyword">TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (price_list_id) <span class="sql-keyword">REFERENCES</span> price_lists(id) <span class="sql-keyword">ON DELETE CASCADE</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (product_id) <span class="sql-keyword">REFERENCES</span> products(id) <span class="sql-keyword">ON DELETE CASCADE</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (product_variant_id) <span class="sql-keyword">REFERENCES</span> product_variants(id) <span class="sql-keyword">ON DELETE CASCADE</span>,
    <span class="sql-keyword">UNIQUE KEY</span> unique_item_price (price_list_id, product_id, product_variant_id),
    <span class="sql-keyword">INDEX</span> idx_item_price_dates (valid_from, valid_upto)
);
            </div>

            <h3>2.9 جدول الدفعات (batches)</h3>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>اسم الحقل</th>
                            <th>نوع البيانات</th>
                            <th>القيود</th>
                            <th>الوصف</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="primary-key">
                            <td>id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>PRIMARY KEY, AUTO_INCREMENT</td>
                            <td>المعرف الفريد للدفعة</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>company_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NOT NULL, FOREIGN KEY</td>
                            <td>معرف الشركة</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>product_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NOT NULL, FOREIGN KEY</td>
                            <td>معرف المنتج</td>
                        </tr>
                        <tr class="required">
                            <td>batch_number</td>
                            <td>VARCHAR(100)</td>
                            <td>NOT NULL</td>
                            <td>رقم الدفعة</td>
                        </tr>
                        <tr class="optional">
                            <td>manufacturing_date</td>
                            <td>DATE</td>
                            <td>NULLABLE</td>
                            <td>تاريخ التصنيع</td>
                        </tr>
                        <tr class="optional">
                            <td>expiry_date</td>
                            <td>DATE</td>
                            <td>NULLABLE</td>
                            <td>تاريخ الانتهاء</td>
                        </tr>
                        <tr class="required">
                            <td>batch_qty</td>
                            <td>DECIMAL(10,3)</td>
                            <td>DEFAULT 0.000</td>
                            <td>كمية الدفعة</td>
                        </tr>
                        <tr class="optional">
                            <td>description</td>
                            <td>TEXT</td>
                            <td>NULLABLE</td>
                            <td>وصف الدفعة</td>
                        </tr>
                        <tr class="required">
                            <td>created_at</td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP</td>
                            <td>تاريخ الإنشاء</td>
                        </tr>
                        <tr class="required">
                            <td>updated_at</td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP ON UPDATE</td>
                            <td>تاريخ التحديث</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="sql-code">
<span class="sql-comment">-- جدول الدفعات</span>
<span class="sql-keyword">CREATE TABLE</span> batches (
    id <span class="sql-keyword">BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT</span>,
    company_id <span class="sql-keyword">BIGINT UNSIGNED NOT NULL</span>,
    product_id <span class="sql-keyword">BIGINT UNSIGNED NOT NULL</span>,
    batch_number <span class="sql-keyword">VARCHAR</span>(100) <span class="sql-keyword">NOT NULL</span>,
    manufacturing_date <span class="sql-keyword">DATE</span>,
    expiry_date <span class="sql-keyword">DATE</span>,
    batch_qty <span class="sql-keyword">DECIMAL</span>(10,3) <span class="sql-keyword">DEFAULT</span> 0.000,
    description <span class="sql-keyword">TEXT</span>,
    created_at <span class="sql-keyword">TIMESTAMP DEFAULT CURRENT_TIMESTAMP</span>,
    updated_at <span class="sql-keyword">TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (company_id) <span class="sql-keyword">REFERENCES</span> companies(id) <span class="sql-keyword">ON DELETE CASCADE</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (product_id) <span class="sql-keyword">REFERENCES</span> products(id),
    <span class="sql-keyword">UNIQUE KEY</span> unique_batch_number (company_id, batch_number),
    <span class="sql-keyword">INDEX</span> idx_batch_expiry (expiry_date),
    <span class="sql-keyword">INDEX</span> idx_batch_product (product_id)
);
            </div>
        </div>
    </div>
</body>
</html>
