<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>جداول التصنيع - نظام ERP للخياطة</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            margin-top: 20px;
            margin-bottom: 20px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            padding: 40px 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 2.5em;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .section {
            margin-bottom: 50px;
            padding: 30px;
            background: #f8f9fa;
            border-radius: 15px;
            border: 1px solid #e9ecef;
        }
        
        .section h2 {
            color: #495057;
            font-size: 2em;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 3px solid #667eea;
        }
        
        .section h3 {
            color: #6c757d;
            font-size: 1.5em;
            margin: 25px 0 15px 0;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border-left: 4px solid #28a745;
        }
        
        .table-container {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin: 20px 0;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            padding: 12px 15px;
            text-align: right;
            border-bottom: 1px solid #dee2e6;
            font-size: 0.9em;
        }
        
        th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: 600;
            position: sticky;
            top: 0;
        }
        
        tr:hover {
            background: #f8f9fa;
        }
        
        .primary-key {
            background: #fff3cd;
            font-weight: bold;
        }
        
        .foreign-key {
            background: #d1ecf1;
        }
        
        .required {
            color: #dc3545;
            font-weight: bold;
        }
        
        .optional {
            color: #6c757d;
        }
        
        .sql-code {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.85em;
            overflow-x: auto;
            margin: 20px 0;
            white-space: pre-wrap;
        }
        
        .sql-keyword {
            color: #63b3ed;
            font-weight: bold;
        }
        
        .sql-string {
            color: #68d391;
        }
        
        .sql-comment {
            color: #a0aec0;
            font-style: italic;
        }
        
        .info-box {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-left: 5px solid #17a2b8;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .nav-links {
            background: #e9ecef;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .nav-links a {
            display: inline-block;
            margin: 5px 10px;
            padding: 10px 20px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-weight: 500;
        }
        
        .nav-links a:hover {
            background: #5a67d8;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>جداول التصنيع</h1>
            <p>نظام ERP لمعامل ومحلات الخياطة</p>
        </div>

        <div class="nav-links">
            <a href="../index.html">الصفحة الرئيسية</a>
            <a href="01_core_tables.html">الجداول الأساسية</a>
            <a href="02_product_inventory_tables.html">المنتجات والمخزون</a>
            <a href="03_sales_pos_tables.html">المبيعات ونقطة البيع</a>
            <a href="04_accounting_tables.html">المحاسبة</a>
            <a href="05_manufacturing_tables.html">التصنيع</a>
            <a href="06_crm_tables.html">إدارة العملاء</a>
            <a href="07_purchasing_tables.html">المشتريات والموردين</a>
            <a href="08_system_tables.html">جداول النظام</a>
        </div>

        <div class="section">
            <h2>5. جداول التصنيع</h2>
            
            <div class="info-box">
                <h4>نظرة عامة:</h4>
                <p>هذه الجداول تدير عمليات التصنيع الكاملة من قوائم المواد (BOM) إلى أوامر العمل وتتبع مراحل الإنتاج في معامل الخياطة.</p>
            </div>

            <h3>5.1 جدول قوائم المواد (bill_of_materials)</h3>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>اسم الحقل</th>
                            <th>نوع البيانات</th>
                            <th>القيود</th>
                            <th>الوصف</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="primary-key">
                            <td>id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>PRIMARY KEY, AUTO_INCREMENT</td>
                            <td>المعرف الفريد لقائمة المواد</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>company_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NOT NULL, FOREIGN KEY</td>
                            <td>معرف الشركة</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>product_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NOT NULL, FOREIGN KEY</td>
                            <td>معرف المنتج النهائي</td>
                        </tr>
                        <tr class="required">
                            <td>bom_code</td>
                            <td>VARCHAR(50)</td>
                            <td>NOT NULL</td>
                            <td>رمز قائمة المواد</td>
                        </tr>
                        <tr class="required">
                            <td>name</td>
                            <td>VARCHAR(255)</td>
                            <td>NOT NULL</td>
                            <td>اسم قائمة المواد</td>
                        </tr>
                        <tr class="required">
                            <td>version</td>
                            <td>VARCHAR(20)</td>
                            <td>DEFAULT '1.0'</td>
                            <td>إصدار قائمة المواد</td>
                        </tr>
                        <tr class="required">
                            <td>quantity</td>
                            <td>DECIMAL(10,3)</td>
                            <td>DEFAULT 1.000</td>
                            <td>الكمية المنتجة</td>
                        </tr>
                        <tr class="optional">
                            <td>description</td>
                            <td>TEXT</td>
                            <td>NULLABLE</td>
                            <td>وصف قائمة المواد</td>
                        </tr>
                        <tr class="required">
                            <td>is_default</td>
                            <td>BOOLEAN</td>
                            <td>DEFAULT FALSE</td>
                            <td>قائمة المواد الافتراضية</td>
                        </tr>
                        <tr class="required">
                            <td>is_active</td>
                            <td>BOOLEAN</td>
                            <td>DEFAULT TRUE</td>
                            <td>حالة النشاط</td>
                        </tr>
                        <tr class="optional">
                            <td>effective_from</td>
                            <td>DATE</td>
                            <td>NULLABLE</td>
                            <td>تاريخ بداية الفعالية</td>
                        </tr>
                        <tr class="optional">
                            <td>effective_to</td>
                            <td>DATE</td>
                            <td>NULLABLE</td>
                            <td>تاريخ نهاية الفعالية</td>
                        </tr>
                        <tr class="required">
                            <td>created_at</td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP</td>
                            <td>تاريخ الإنشاء</td>
                        </tr>
                        <tr class="required">
                            <td>updated_at</td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP ON UPDATE</td>
                            <td>تاريخ التحديث</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="sql-code">
<span class="sql-comment">-- جدول قوائم المواد</span>
<span class="sql-keyword">CREATE TABLE</span> bill_of_materials (
    id <span class="sql-keyword">BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT</span>,
    company_id <span class="sql-keyword">BIGINT UNSIGNED NOT NULL</span>,
    product_id <span class="sql-keyword">BIGINT UNSIGNED NOT NULL</span>,
    bom_code <span class="sql-keyword">VARCHAR</span>(50) <span class="sql-keyword">NOT NULL</span>,
    name <span class="sql-keyword">VARCHAR</span>(255) <span class="sql-keyword">NOT NULL</span>,
    version <span class="sql-keyword">VARCHAR</span>(20) <span class="sql-keyword">DEFAULT</span> <span class="sql-string">'1.0'</span>,
    quantity <span class="sql-keyword">DECIMAL</span>(10,3) <span class="sql-keyword">DEFAULT</span> 1.000,
    description <span class="sql-keyword">TEXT</span>,
    is_default <span class="sql-keyword">BOOLEAN DEFAULT FALSE</span>,
    is_active <span class="sql-keyword">BOOLEAN DEFAULT TRUE</span>,
    effective_from <span class="sql-keyword">DATE</span>,
    effective_to <span class="sql-keyword">DATE</span>,
    created_at <span class="sql-keyword">TIMESTAMP DEFAULT CURRENT_TIMESTAMP</span>,
    updated_at <span class="sql-keyword">TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (company_id) <span class="sql-keyword">REFERENCES</span> companies(id) <span class="sql-keyword">ON DELETE CASCADE</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (product_id) <span class="sql-keyword">REFERENCES</span> products(id) <span class="sql-keyword">ON DELETE CASCADE</span>,
    <span class="sql-keyword">UNIQUE KEY</span> unique_bom_code (company_id, bom_code),
    <span class="sql-keyword">INDEX</span> idx_bom_product (product_id),
    <span class="sql-keyword">INDEX</span> idx_bom_active (is_active)
);
            </div>

            <h3>5.2 جدول مكونات قوائم المواد (bom_items)</h3>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>اسم الحقل</th>
                            <th>نوع البيانات</th>
                            <th>القيود</th>
                            <th>الوصف</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="primary-key">
                            <td>id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>PRIMARY KEY, AUTO_INCREMENT</td>
                            <td>المعرف الفريد للمكون</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>bom_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NOT NULL, FOREIGN KEY</td>
                            <td>معرف قائمة المواد</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>product_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NOT NULL, FOREIGN KEY</td>
                            <td>معرف المنتج/المادة الخام</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>product_variant_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NULLABLE, FOREIGN KEY</td>
                            <td>معرف متغير المنتج</td>
                        </tr>
                        <tr class="required">
                            <td>quantity_required</td>
                            <td>DECIMAL(10,3)</td>
                            <td>NOT NULL</td>
                            <td>الكمية المطلوبة</td>
                        </tr>
                        <tr class="required">
                            <td>unit_cost</td>
                            <td>DECIMAL(12,2)</td>
                            <td>DEFAULT 0.00</td>
                            <td>تكلفة الوحدة</td>
                        </tr>
                        <tr class="required">
                            <td>total_cost</td>
                            <td>DECIMAL(12,2)</td>
                            <td>DEFAULT 0.00</td>
                            <td>التكلفة الإجمالية</td>
                        </tr>
                        <tr class="required">
                            <td>wastage_percentage</td>
                            <td>DECIMAL(5,2)</td>
                            <td>DEFAULT 0.00</td>
                            <td>نسبة الفاقد</td>
                        </tr>
                        <tr class="optional">
                            <td>notes</td>
                            <td>TEXT</td>
                            <td>NULLABLE</td>
                            <td>ملاحظات</td>
                        </tr>
                        <tr class="required">
                            <td>is_optional</td>
                            <td>BOOLEAN</td>
                            <td>DEFAULT FALSE</td>
                            <td>مكون اختياري</td>
                        </tr>
                        <tr class="required">
                            <td>created_at</td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP</td>
                            <td>تاريخ الإنشاء</td>
                        </tr>
                        <tr class="required">
                            <td>updated_at</td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP ON UPDATE</td>
                            <td>تاريخ التحديث</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="sql-code">
<span class="sql-comment">-- جدول مكونات قوائم المواد</span>
<span class="sql-keyword">CREATE TABLE</span> bom_items (
    id <span class="sql-keyword">BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT</span>,
    bom_id <span class="sql-keyword">BIGINT UNSIGNED NOT NULL</span>,
    product_id <span class="sql-keyword">BIGINT UNSIGNED NOT NULL</span>,
    product_variant_id <span class="sql-keyword">BIGINT UNSIGNED</span>,
    quantity_required <span class="sql-keyword">DECIMAL</span>(10,3) <span class="sql-keyword">NOT NULL</span>,
    unit_cost <span class="sql-keyword">DECIMAL</span>(12,2) <span class="sql-keyword">DEFAULT</span> 0.00,
    total_cost <span class="sql-keyword">DECIMAL</span>(12,2) <span class="sql-keyword">DEFAULT</span> 0.00,
    wastage_percentage <span class="sql-keyword">DECIMAL</span>(5,2) <span class="sql-keyword">DEFAULT</span> 0.00,
    notes <span class="sql-keyword">TEXT</span>,
    is_optional <span class="sql-keyword">BOOLEAN DEFAULT FALSE</span>,
    created_at <span class="sql-keyword">TIMESTAMP DEFAULT CURRENT_TIMESTAMP</span>,
    updated_at <span class="sql-keyword">TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (bom_id) <span class="sql-keyword">REFERENCES</span> bill_of_materials(id) <span class="sql-keyword">ON DELETE CASCADE</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (product_id) <span class="sql-keyword">REFERENCES</span> products(id),
    <span class="sql-keyword">FOREIGN KEY</span> (product_variant_id) <span class="sql-keyword">REFERENCES</span> product_variants(id),
    <span class="sql-keyword">INDEX</span> idx_bom_items (bom_id),
    <span class="sql-keyword">INDEX</span> idx_product_bom (product_id)
);
            </div>

            <h3>5.3 جدول أوامر العمل (work_orders)</h3>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>اسم الحقل</th>
                            <th>نوع البيانات</th>
                            <th>القيود</th>
                            <th>الوصف</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="primary-key">
                            <td>id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>PRIMARY KEY, AUTO_INCREMENT</td>
                            <td>المعرف الفريد لأمر العمل</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>company_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NOT NULL, FOREIGN KEY</td>
                            <td>معرف الشركة</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>branch_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NOT NULL, FOREIGN KEY</td>
                            <td>معرف الفرع</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>bom_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NOT NULL, FOREIGN KEY</td>
                            <td>معرف قائمة المواد</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>sales_order_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NULLABLE, FOREIGN KEY</td>
                            <td>معرف أمر البيع</td>
                        </tr>
                        <tr class="required">
                            <td>work_order_number</td>
                            <td>VARCHAR(50)</td>
                            <td>NOT NULL</td>
                            <td>رقم أمر العمل</td>
                        </tr>
                        <tr class="required">
                            <td>planned_start_date</td>
                            <td>DATE</td>
                            <td>NOT NULL</td>
                            <td>تاريخ البداية المخطط</td>
                        </tr>
                        <tr class="required">
                            <td>planned_end_date</td>
                            <td>DATE</td>
                            <td>NOT NULL</td>
                            <td>تاريخ النهاية المخطط</td>
                        </tr>
                        <tr class="optional">
                            <td>actual_start_date</td>
                            <td>DATE</td>
                            <td>NULLABLE</td>
                            <td>تاريخ البداية الفعلي</td>
                        </tr>
                        <tr class="optional">
                            <td>actual_end_date</td>
                            <td>DATE</td>
                            <td>NULLABLE</td>
                            <td>تاريخ النهاية الفعلي</td>
                        </tr>
                        <tr class="required">
                            <td>quantity_to_produce</td>
                            <td>DECIMAL(10,3)</td>
                            <td>NOT NULL</td>
                            <td>الكمية المطلوب إنتاجها</td>
                        </tr>
                        <tr class="required">
                            <td>quantity_produced</td>
                            <td>DECIMAL(10,3)</td>
                            <td>DEFAULT 0.000</td>
                            <td>الكمية المنتجة</td>
                        </tr>
                        <tr class="required">
                            <td>status</td>
                            <td>ENUM</td>
                            <td>('draft','released','in_progress','completed','cancelled')</td>
                            <td>حالة أمر العمل</td>
                        </tr>
                        <tr class="required">
                            <td>priority</td>
                            <td>ENUM</td>
                            <td>('low','normal','high','urgent')</td>
                            <td>الأولوية</td>
                        </tr>
                        <tr class="optional">
                            <td>notes</td>
                            <td>TEXT</td>
                            <td>NULLABLE</td>
                            <td>ملاحظات</td>
                        </tr>
                        <tr class="required">
                            <td>created_at</td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP</td>
                            <td>تاريخ الإنشاء</td>
                        </tr>
                        <tr class="required">
                            <td>updated_at</td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP ON UPDATE</td>
                            <td>تاريخ التحديث</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="sql-code">
<span class="sql-comment">-- جدول أوامر العمل</span>
<span class="sql-keyword">CREATE TABLE</span> work_orders (
    id <span class="sql-keyword">BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT</span>,
    company_id <span class="sql-keyword">BIGINT UNSIGNED NOT NULL</span>,
    branch_id <span class="sql-keyword">BIGINT UNSIGNED NOT NULL</span>,
    bom_id <span class="sql-keyword">BIGINT UNSIGNED NOT NULL</span>,
    sales_order_id <span class="sql-keyword">BIGINT UNSIGNED</span>,
    work_order_number <span class="sql-keyword">VARCHAR</span>(50) <span class="sql-keyword">NOT NULL</span>,
    planned_start_date <span class="sql-keyword">DATE NOT NULL</span>,
    planned_end_date <span class="sql-keyword">DATE NOT NULL</span>,
    actual_start_date <span class="sql-keyword">DATE</span>,
    actual_end_date <span class="sql-keyword">DATE</span>,
    quantity_to_produce <span class="sql-keyword">DECIMAL</span>(10,3) <span class="sql-keyword">NOT NULL</span>,
    quantity_produced <span class="sql-keyword">DECIMAL</span>(10,3) <span class="sql-keyword">DEFAULT</span> 0.000,
    status <span class="sql-keyword">ENUM</span>(<span class="sql-string">'draft','released','in_progress','completed','cancelled'</span>) <span class="sql-keyword">DEFAULT</span> <span class="sql-string">'draft'</span>,
    priority <span class="sql-keyword">ENUM</span>(<span class="sql-string">'low','normal','high','urgent'</span>) <span class="sql-keyword">DEFAULT</span> <span class="sql-string">'normal'</span>,
    notes <span class="sql-keyword">TEXT</span>,
    created_at <span class="sql-keyword">TIMESTAMP DEFAULT CURRENT_TIMESTAMP</span>,
    updated_at <span class="sql-keyword">TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (company_id) <span class="sql-keyword">REFERENCES</span> companies(id) <span class="sql-keyword">ON DELETE CASCADE</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (branch_id) <span class="sql-keyword">REFERENCES</span> branches(id) <span class="sql-keyword">ON DELETE CASCADE</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (bom_id) <span class="sql-keyword">REFERENCES</span> bill_of_materials(id),
    <span class="sql-keyword">FOREIGN KEY</span> (sales_order_id) <span class="sql-keyword">REFERENCES</span> sales_orders(id) <span class="sql-keyword">ON DELETE SET NULL</span>,
    <span class="sql-keyword">UNIQUE KEY</span> unique_work_order_number (company_id, work_order_number),
    <span class="sql-keyword">INDEX</span> idx_work_order_status (status),
    <span class="sql-keyword">INDEX</span> idx_work_order_dates (planned_start_date, planned_end_date),
    <span class="sql-keyword">INDEX</span> idx_work_order_priority (priority)
);
            </div>
        </div>
    </div>
</body>
</html>
