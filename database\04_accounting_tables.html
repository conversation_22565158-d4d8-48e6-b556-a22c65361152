<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>جداول المحاسبة - نظام ERP للخياطة</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            margin-top: 20px;
            margin-bottom: 20px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            padding: 40px 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 2.5em;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .section {
            margin-bottom: 50px;
            padding: 30px;
            background: #f8f9fa;
            border-radius: 15px;
            border: 1px solid #e9ecef;
        }
        
        .section h2 {
            color: #495057;
            font-size: 2em;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 3px solid #667eea;
        }
        
        .section h3 {
            color: #6c757d;
            font-size: 1.5em;
            margin: 25px 0 15px 0;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border-left: 4px solid #28a745;
        }
        
        .table-container {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin: 20px 0;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            padding: 12px 15px;
            text-align: right;
            border-bottom: 1px solid #dee2e6;
            font-size: 0.9em;
        }
        
        th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: 600;
            position: sticky;
            top: 0;
        }
        
        tr:hover {
            background: #f8f9fa;
        }
        
        .primary-key {
            background: #fff3cd;
            font-weight: bold;
        }
        
        .foreign-key {
            background: #d1ecf1;
        }
        
        .required {
            color: #dc3545;
            font-weight: bold;
        }
        
        .optional {
            color: #6c757d;
        }
        
        .sql-code {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.85em;
            overflow-x: auto;
            margin: 20px 0;
            white-space: pre-wrap;
        }
        
        .sql-keyword {
            color: #63b3ed;
            font-weight: bold;
        }
        
        .sql-string {
            color: #68d391;
        }
        
        .sql-comment {
            color: #a0aec0;
            font-style: italic;
        }
        
        .info-box {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-left: 5px solid #17a2b8;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .nav-links {
            background: #e9ecef;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .nav-links a {
            display: inline-block;
            margin: 5px 10px;
            padding: 10px 20px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-weight: 500;
        }
        
        .nav-links a:hover {
            background: #5a67d8;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>جداول المحاسبة</h1>
            <p>نظام ERP لمعامل ومحلات الخياطة</p>
        </div>

        <div class="nav-links">
            <a href="../index.html">الصفحة الرئيسية</a>
            <a href="01_core_tables.html">الجداول الأساسية</a>
            <a href="02_product_inventory_tables.html">المنتجات والمخزون</a>
            <a href="03_sales_pos_tables.html">المبيعات ونقطة البيع</a>
            <a href="04_accounting_tables.html">المحاسبة</a>
            <a href="05_manufacturing_tables.html">التصنيع</a>
            <a href="06_crm_tables.html">إدارة العملاء</a>
            <a href="07_purchasing_tables.html">المشتريات والموردين</a>
            <a href="08_system_tables.html">جداول النظام</a>
        </div>

        <div class="section">
            <h2>4. جداول المحاسبة</h2>
            
            <div class="info-box">
                <h4>نظرة عامة:</h4>
                <p>هذه الجداول تدير النظام المحاسبي الكامل بما في ذلك دليل الحسابات، القيود المحاسبية، والتقارير المالية.</p>
            </div>

            <h3>4.1 جدول دليل الحسابات (chart_of_accounts)</h3>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>اسم الحقل</th>
                            <th>نوع البيانات</th>
                            <th>القيود</th>
                            <th>الوصف</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="primary-key">
                            <td>id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>PRIMARY KEY, AUTO_INCREMENT</td>
                            <td>المعرف الفريد للحساب</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>company_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NOT NULL, FOREIGN KEY</td>
                            <td>معرف الشركة</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>parent_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NULLABLE, FOREIGN KEY</td>
                            <td>معرف الحساب الأب</td>
                        </tr>
                        <tr class="required">
                            <td>account_code</td>
                            <td>VARCHAR(20)</td>
                            <td>NOT NULL</td>
                            <td>رمز الحساب</td>
                        </tr>
                        <tr class="required">
                            <td>account_name</td>
                            <td>VARCHAR(255)</td>
                            <td>NOT NULL</td>
                            <td>اسم الحساب</td>
                        </tr>
                        <tr class="required">
                            <td>account_type</td>
                            <td>ENUM</td>
                            <td>('asset','liability','equity','revenue','expense')</td>
                            <td>نوع الحساب</td>
                        </tr>
                        <tr class="required">
                            <td>account_subtype</td>
                            <td>VARCHAR(100)</td>
                            <td>NOT NULL</td>
                            <td>النوع الفرعي للحساب</td>
                        </tr>
                        <tr class="required">
                            <td>normal_balance</td>
                            <td>ENUM</td>
                            <td>('debit','credit')</td>
                            <td>الرصيد الطبيعي</td>
                        </tr>
                        <tr class="required">
                            <td>is_header</td>
                            <td>BOOLEAN</td>
                            <td>DEFAULT FALSE</td>
                            <td>حساب رئيسي</td>
                        </tr>
                        <tr class="required">
                            <td>level</td>
                            <td>INT</td>
                            <td>DEFAULT 1</td>
                            <td>مستوى الحساب</td>
                        </tr>
                        <tr class="optional">
                            <td>description</td>
                            <td>TEXT</td>
                            <td>NULLABLE</td>
                            <td>وصف الحساب</td>
                        </tr>
                        <tr class="required">
                            <td>is_system_account</td>
                            <td>BOOLEAN</td>
                            <td>DEFAULT FALSE</td>
                            <td>حساب نظام</td>
                        </tr>
                        <tr class="required">
                            <td>is_active</td>
                            <td>BOOLEAN</td>
                            <td>DEFAULT TRUE</td>
                            <td>حالة النشاط</td>
                        </tr>
                        <tr class="required">
                            <td>created_at</td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP</td>
                            <td>تاريخ الإنشاء</td>
                        </tr>
                        <tr class="required">
                            <td>updated_at</td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP ON UPDATE</td>
                            <td>تاريخ التحديث</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="sql-code">
<span class="sql-comment">-- جدول دليل الحسابات</span>
<span class="sql-keyword">CREATE TABLE</span> chart_of_accounts (
    id <span class="sql-keyword">BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT</span>,
    company_id <span class="sql-keyword">BIGINT UNSIGNED NOT NULL</span>,
    parent_id <span class="sql-keyword">BIGINT UNSIGNED</span>,
    account_code <span class="sql-keyword">VARCHAR</span>(20) <span class="sql-keyword">NOT NULL</span>,
    account_name <span class="sql-keyword">VARCHAR</span>(255) <span class="sql-keyword">NOT NULL</span>,
    account_type <span class="sql-keyword">ENUM</span>(<span class="sql-string">'asset','liability','equity','revenue','expense'</span>) <span class="sql-keyword">NOT NULL</span>,
    account_subtype <span class="sql-keyword">VARCHAR</span>(100) <span class="sql-keyword">NOT NULL</span>,
    normal_balance <span class="sql-keyword">ENUM</span>(<span class="sql-string">'debit','credit'</span>) <span class="sql-keyword">NOT NULL</span>,
    is_header <span class="sql-keyword">BOOLEAN DEFAULT FALSE</span>,
    level <span class="sql-keyword">INT DEFAULT</span> 1,
    description <span class="sql-keyword">TEXT</span>,
    is_system_account <span class="sql-keyword">BOOLEAN DEFAULT FALSE</span>,
    is_active <span class="sql-keyword">BOOLEAN DEFAULT TRUE</span>,
    created_at <span class="sql-keyword">TIMESTAMP DEFAULT CURRENT_TIMESTAMP</span>,
    updated_at <span class="sql-keyword">TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (company_id) <span class="sql-keyword">REFERENCES</span> companies(id) <span class="sql-keyword">ON DELETE CASCADE</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (parent_id) <span class="sql-keyword">REFERENCES</span> chart_of_accounts(id) <span class="sql-keyword">ON DELETE SET NULL</span>,
    <span class="sql-keyword">UNIQUE KEY</span> unique_account_code (company_id, account_code),
    <span class="sql-keyword">INDEX</span> idx_account_type (account_type),
    <span class="sql-keyword">INDEX</span> idx_account_level (level)
);
            </div>

            <h3>4.2 جدول القيود المحاسبية (journal_entries)</h3>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>اسم الحقل</th>
                            <th>نوع البيانات</th>
                            <th>القيود</th>
                            <th>الوصف</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="primary-key">
                            <td>id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>PRIMARY KEY, AUTO_INCREMENT</td>
                            <td>المعرف الفريد للقيد</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>company_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NOT NULL, FOREIGN KEY</td>
                            <td>معرف الشركة</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>branch_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NOT NULL, FOREIGN KEY</td>
                            <td>معرف الفرع</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>user_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NOT NULL, FOREIGN KEY</td>
                            <td>معرف المستخدم</td>
                        </tr>
                        <tr class="required">
                            <td>entry_number</td>
                            <td>VARCHAR(50)</td>
                            <td>NOT NULL</td>
                            <td>رقم القيد</td>
                        </tr>
                        <tr class="required">
                            <td>entry_date</td>
                            <td>DATE</td>
                            <td>NOT NULL</td>
                            <td>تاريخ القيد</td>
                        </tr>
                        <tr class="required">
                            <td>entry_type</td>
                            <td>ENUM</td>
                            <td>('manual','automatic','adjustment','closing')</td>
                            <td>نوع القيد</td>
                        </tr>
                        <tr class="optional">
                            <td>reference_type</td>
                            <td>VARCHAR(50)</td>
                            <td>NULLABLE</td>
                            <td>نوع المرجع</td>
                        </tr>
                        <tr class="optional">
                            <td>reference_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NULLABLE</td>
                            <td>معرف المرجع</td>
                        </tr>
                        <tr class="required">
                            <td>description</td>
                            <td>TEXT</td>
                            <td>NOT NULL</td>
                            <td>وصف القيد</td>
                        </tr>
                        <tr class="required">
                            <td>total_debit</td>
                            <td>DECIMAL(15,2)</td>
                            <td>DEFAULT 0.00</td>
                            <td>إجمالي المدين</td>
                        </tr>
                        <tr class="required">
                            <td>total_credit</td>
                            <td>DECIMAL(15,2)</td>
                            <td>DEFAULT 0.00</td>
                            <td>إجمالي الدائن</td>
                        </tr>
                        <tr class="required">
                            <td>status</td>
                            <td>ENUM</td>
                            <td>('draft','posted','reversed')</td>
                            <td>حالة القيد</td>
                        </tr>
                        <tr class="optional">
                            <td>posted_at</td>
                            <td>TIMESTAMP</td>
                            <td>NULLABLE</td>
                            <td>تاريخ الترحيل</td>
                        </tr>
                        <tr class="optional">
                            <td>reversed_at</td>
                            <td>TIMESTAMP</td>
                            <td>NULLABLE</td>
                            <td>تاريخ العكس</td>
                        </tr>
                        <tr class="required">
                            <td>created_at</td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP</td>
                            <td>تاريخ الإنشاء</td>
                        </tr>
                        <tr class="required">
                            <td>updated_at</td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP ON UPDATE</td>
                            <td>تاريخ التحديث</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="sql-code">
<span class="sql-comment">-- جدول القيود المحاسبية</span>
<span class="sql-keyword">CREATE TABLE</span> journal_entries (
    id <span class="sql-keyword">BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT</span>,
    company_id <span class="sql-keyword">BIGINT UNSIGNED NOT NULL</span>,
    branch_id <span class="sql-keyword">BIGINT UNSIGNED NOT NULL</span>,
    user_id <span class="sql-keyword">BIGINT UNSIGNED NOT NULL</span>,
    entry_number <span class="sql-keyword">VARCHAR</span>(50) <span class="sql-keyword">NOT NULL</span>,
    entry_date <span class="sql-keyword">DATE NOT NULL</span>,
    entry_type <span class="sql-keyword">ENUM</span>(<span class="sql-string">'manual','automatic','adjustment','closing'</span>) <span class="sql-keyword">DEFAULT</span> <span class="sql-string">'manual'</span>,
    reference_type <span class="sql-keyword">VARCHAR</span>(50),
    reference_id <span class="sql-keyword">BIGINT UNSIGNED</span>,
    description <span class="sql-keyword">TEXT NOT NULL</span>,
    total_debit <span class="sql-keyword">DECIMAL</span>(15,2) <span class="sql-keyword">DEFAULT</span> 0.00,
    total_credit <span class="sql-keyword">DECIMAL</span>(15,2) <span class="sql-keyword">DEFAULT</span> 0.00,
    status <span class="sql-keyword">ENUM</span>(<span class="sql-string">'draft','posted','reversed'</span>) <span class="sql-keyword">DEFAULT</span> <span class="sql-string">'draft'</span>,
    posted_at <span class="sql-keyword">TIMESTAMP</span>,
    reversed_at <span class="sql-keyword">TIMESTAMP</span>,
    created_at <span class="sql-keyword">TIMESTAMP DEFAULT CURRENT_TIMESTAMP</span>,
    updated_at <span class="sql-keyword">TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (company_id) <span class="sql-keyword">REFERENCES</span> companies(id) <span class="sql-keyword">ON DELETE CASCADE</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (branch_id) <span class="sql-keyword">REFERENCES</span> branches(id) <span class="sql-keyword">ON DELETE CASCADE</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (user_id) <span class="sql-keyword">REFERENCES</span> users(id),
    <span class="sql-keyword">UNIQUE KEY</span> unique_entry_number (company_id, entry_number),
    <span class="sql-keyword">INDEX</span> idx_entry_date (entry_date),
    <span class="sql-keyword">INDEX</span> idx_entry_type (entry_type),
    <span class="sql-keyword">INDEX</span> idx_entry_status (status),
    <span class="sql-keyword">INDEX</span> idx_reference (reference_type, reference_id)
);
            </div>

            <h3>4.3 جدول تفاصيل القيود المحاسبية (journal_entry_lines)</h3>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>اسم الحقل</th>
                            <th>نوع البيانات</th>
                            <th>القيود</th>
                            <th>الوصف</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="primary-key">
                            <td>id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>PRIMARY KEY, AUTO_INCREMENT</td>
                            <td>المعرف الفريد للسطر</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>journal_entry_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NOT NULL, FOREIGN KEY</td>
                            <td>معرف القيد المحاسبي</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>account_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NOT NULL, FOREIGN KEY</td>
                            <td>معرف الحساب</td>
                        </tr>
                        <tr class="required">
                            <td>line_number</td>
                            <td>INT</td>
                            <td>NOT NULL</td>
                            <td>رقم السطر</td>
                        </tr>
                        <tr class="required">
                            <td>description</td>
                            <td>VARCHAR(255)</td>
                            <td>NOT NULL</td>
                            <td>وصف السطر</td>
                        </tr>
                        <tr class="required">
                            <td>debit_amount</td>
                            <td>DECIMAL(15,2)</td>
                            <td>DEFAULT 0.00</td>
                            <td>المبلغ المدين</td>
                        </tr>
                        <tr class="required">
                            <td>credit_amount</td>
                            <td>DECIMAL(15,2)</td>
                            <td>DEFAULT 0.00</td>
                            <td>المبلغ الدائن</td>
                        </tr>
                        <tr class="optional">
                            <td>cost_center_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NULLABLE, FOREIGN KEY</td>
                            <td>معرف مركز التكلفة</td>
                        </tr>
                        <tr class="optional">
                            <td>project_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NULLABLE</td>
                            <td>معرف المشروع</td>
                        </tr>
                        <tr class="required">
                            <td>created_at</td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP</td>
                            <td>تاريخ الإنشاء</td>
                        </tr>
                        <tr class="required">
                            <td>updated_at</td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP ON UPDATE</td>
                            <td>تاريخ التحديث</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="sql-code">
<span class="sql-comment">-- جدول تفاصيل القيود المحاسبية</span>
<span class="sql-keyword">CREATE TABLE</span> journal_entry_lines (
    id <span class="sql-keyword">BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT</span>,
    journal_entry_id <span class="sql-keyword">BIGINT UNSIGNED NOT NULL</span>,
    account_id <span class="sql-keyword">BIGINT UNSIGNED NOT NULL</span>,
    line_number <span class="sql-keyword">INT NOT NULL</span>,
    description <span class="sql-keyword">VARCHAR</span>(255) <span class="sql-keyword">NOT NULL</span>,
    debit_amount <span class="sql-keyword">DECIMAL</span>(15,2) <span class="sql-keyword">DEFAULT</span> 0.00,
    credit_amount <span class="sql-keyword">DECIMAL</span>(15,2) <span class="sql-keyword">DEFAULT</span> 0.00,
    cost_center_id <span class="sql-keyword">BIGINT UNSIGNED</span>,
    project_id <span class="sql-keyword">BIGINT UNSIGNED</span>,
    created_at <span class="sql-keyword">TIMESTAMP DEFAULT CURRENT_TIMESTAMP</span>,
    updated_at <span class="sql-keyword">TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (journal_entry_id) <span class="sql-keyword">REFERENCES</span> journal_entries(id) <span class="sql-keyword">ON DELETE CASCADE</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (account_id) <span class="sql-keyword">REFERENCES</span> chart_of_accounts(id),
    <span class="sql-keyword">INDEX</span> idx_journal_lines (journal_entry_id),
    <span class="sql-keyword">INDEX</span> idx_account_lines (account_id),
    <span class="sql-keyword">INDEX</span> idx_line_number (journal_entry_id, line_number)
);
            </div>
        </div>
    </div>
</body>
</html>
