<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>جداول المبيعات ونقطة البيع - نظام ERP للخياطة</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            margin-top: 20px;
            margin-bottom: 20px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            padding: 40px 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 2.5em;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .section {
            margin-bottom: 50px;
            padding: 30px;
            background: #f8f9fa;
            border-radius: 15px;
            border: 1px solid #e9ecef;
        }
        
        .section h2 {
            color: #495057;
            font-size: 2em;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 3px solid #667eea;
        }
        
        .section h3 {
            color: #6c757d;
            font-size: 1.5em;
            margin: 25px 0 15px 0;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border-left: 4px solid #28a745;
        }
        
        .table-container {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin: 20px 0;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            padding: 12px 15px;
            text-align: right;
            border-bottom: 1px solid #dee2e6;
            font-size: 0.9em;
        }
        
        th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: 600;
            position: sticky;
            top: 0;
        }
        
        tr:hover {
            background: #f8f9fa;
        }
        
        .primary-key {
            background: #fff3cd;
            font-weight: bold;
        }
        
        .foreign-key {
            background: #d1ecf1;
        }
        
        .required {
            color: #dc3545;
            font-weight: bold;
        }
        
        .optional {
            color: #6c757d;
        }
        
        .sql-code {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.85em;
            overflow-x: auto;
            margin: 20px 0;
            white-space: pre-wrap;
        }
        
        .sql-keyword {
            color: #63b3ed;
            font-weight: bold;
        }
        
        .sql-string {
            color: #68d391;
        }
        
        .sql-comment {
            color: #a0aec0;
            font-style: italic;
        }
        
        .info-box {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-left: 5px solid #17a2b8;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .nav-links {
            background: #e9ecef;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .nav-links a {
            display: inline-block;
            margin: 5px 10px;
            padding: 10px 20px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-weight: 500;
        }
        
        .nav-links a:hover {
            background: #5a67d8;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>جداول المبيعات ونقطة البيع</h1>
            <p>نظام ERP لمعامل ومحلات الخياطة</p>
        </div>

        <div class="nav-links">
            <a href="../index.html">الصفحة الرئيسية</a>
            <a href="01_core_tables.html">الجداول الأساسية</a>
            <a href="02_product_inventory_tables.html">المنتجات والمخزون</a>
            <a href="03_sales_pos_tables.html">المبيعات ونقطة البيع</a>
            <a href="04_accounting_tables.html">المحاسبة</a>
            <a href="05_manufacturing_tables.html">التصنيع</a>
            <a href="06_crm_tables.html">إدارة العملاء</a>
            <a href="07_purchasing_tables.html">المشتريات والموردين</a>
            <a href="08_system_tables.html">جداول النظام</a>
        </div>

        <div class="section">
            <h2>3. جداول المبيعات ونقطة البيع</h2>
            
            <div class="info-box">
                <h4>نظرة عامة:</h4>
                <p>هذه الجداول تدير جميع عمليات المبيعات من العروض والطلبات إلى الفواتير والمدفوعات، مع دعم نقطة البيع والعمل offline.</p>
            </div>

            <h3>3.1 جدول العملاء (customers)</h3>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>اسم الحقل</th>
                            <th>نوع البيانات</th>
                            <th>القيود</th>
                            <th>الوصف</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="primary-key">
                            <td>id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>PRIMARY KEY, AUTO_INCREMENT</td>
                            <td>المعرف الفريد للعميل</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>company_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NOT NULL, FOREIGN KEY</td>
                            <td>معرف الشركة</td>
                        </tr>
                        <tr class="required">
                            <td>customer_code</td>
                            <td>VARCHAR(50)</td>
                            <td>NOT NULL</td>
                            <td>رمز العميل</td>
                        </tr>
                        <tr class="required">
                            <td>name</td>
                            <td>VARCHAR(255)</td>
                            <td>NOT NULL</td>
                            <td>اسم العميل</td>
                        </tr>
                        <tr class="required">
                            <td>customer_type</td>
                            <td>ENUM</td>
                            <td>('individual','company')</td>
                            <td>نوع العميل</td>
                        </tr>
                        <tr class="optional">
                            <td>phone</td>
                            <td>VARCHAR(20)</td>
                            <td>NULLABLE</td>
                            <td>رقم الهاتف</td>
                        </tr>
                        <tr class="optional">
                            <td>email</td>
                            <td>VARCHAR(255)</td>
                            <td>NULLABLE</td>
                            <td>البريد الإلكتروني</td>
                        </tr>
                        <tr class="optional">
                            <td>address</td>
                            <td>TEXT</td>
                            <td>NULLABLE</td>
                            <td>العنوان</td>
                        </tr>
                        <tr class="optional">
                            <td>city</td>
                            <td>VARCHAR(100)</td>
                            <td>NULLABLE</td>
                            <td>المدينة</td>
                        </tr>
                        <tr class="optional">
                            <td>tax_number</td>
                            <td>VARCHAR(50)</td>
                            <td>NULLABLE</td>
                            <td>الرقم الضريبي</td>
                        </tr>
                        <tr class="required">
                            <td>credit_limit</td>
                            <td>DECIMAL(12,2)</td>
                            <td>DEFAULT 0.00</td>
                            <td>حد الائتمان</td>
                        </tr>
                        <tr class="required">
                            <td>payment_terms</td>
                            <td>INT</td>
                            <td>DEFAULT 0</td>
                            <td>شروط الدفع (بالأيام)</td>
                        </tr>
                        <tr class="optional">
                            <td>discount_percentage</td>
                            <td>DECIMAL(5,2)</td>
                            <td>DEFAULT 0.00</td>
                            <td>نسبة الخصم</td>
                        </tr>
                        <tr class="required">
                            <td>is_active</td>
                            <td>BOOLEAN</td>
                            <td>DEFAULT TRUE</td>
                            <td>حالة النشاط</td>
                        </tr>
                        <tr class="optional">
                            <td>notes</td>
                            <td>TEXT</td>
                            <td>NULLABLE</td>
                            <td>ملاحظات</td>
                        </tr>
                        <tr class="required">
                            <td>created_at</td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP</td>
                            <td>تاريخ الإنشاء</td>
                        </tr>
                        <tr class="required">
                            <td>updated_at</td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP ON UPDATE</td>
                            <td>تاريخ التحديث</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="sql-code">
<span class="sql-comment">-- جدول العملاء</span>
<span class="sql-keyword">CREATE TABLE</span> customers (
    id <span class="sql-keyword">BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT</span>,
    company_id <span class="sql-keyword">BIGINT UNSIGNED NOT NULL</span>,
    customer_code <span class="sql-keyword">VARCHAR</span>(50) <span class="sql-keyword">NOT NULL</span>,
    name <span class="sql-keyword">VARCHAR</span>(255) <span class="sql-keyword">NOT NULL</span>,
    customer_type <span class="sql-keyword">ENUM</span>(<span class="sql-string">'individual','company'</span>) <span class="sql-keyword">DEFAULT</span> <span class="sql-string">'individual'</span>,
    phone <span class="sql-keyword">VARCHAR</span>(20),
    email <span class="sql-keyword">VARCHAR</span>(255),
    address <span class="sql-keyword">TEXT</span>,
    city <span class="sql-keyword">VARCHAR</span>(100),
    tax_number <span class="sql-keyword">VARCHAR</span>(50),
    credit_limit <span class="sql-keyword">DECIMAL</span>(12,2) <span class="sql-keyword">DEFAULT</span> 0.00,
    payment_terms <span class="sql-keyword">INT DEFAULT</span> 0,
    discount_percentage <span class="sql-keyword">DECIMAL</span>(5,2) <span class="sql-keyword">DEFAULT</span> 0.00,
    is_active <span class="sql-keyword">BOOLEAN DEFAULT TRUE</span>,
    notes <span class="sql-keyword">TEXT</span>,
    created_at <span class="sql-keyword">TIMESTAMP DEFAULT CURRENT_TIMESTAMP</span>,
    updated_at <span class="sql-keyword">TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (company_id) <span class="sql-keyword">REFERENCES</span> companies(id) <span class="sql-keyword">ON DELETE CASCADE</span>,
    <span class="sql-keyword">UNIQUE KEY</span> unique_customer_code (company_id, customer_code),
    <span class="sql-keyword">INDEX</span> idx_customer_phone (phone),
    <span class="sql-keyword">INDEX</span> idx_customer_email (email)
);
            </div>

            <h3>3.2 جدول أوامر البيع (sales_orders)</h3>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>اسم الحقل</th>
                            <th>نوع البيانات</th>
                            <th>القيود</th>
                            <th>الوصف</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="primary-key">
                            <td>id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>PRIMARY KEY, AUTO_INCREMENT</td>
                            <td>المعرف الفريد لأمر البيع</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>company_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NOT NULL, FOREIGN KEY</td>
                            <td>معرف الشركة</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>branch_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NOT NULL, FOREIGN KEY</td>
                            <td>معرف الفرع</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>customer_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NOT NULL, FOREIGN KEY</td>
                            <td>معرف العميل</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>user_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NOT NULL, FOREIGN KEY</td>
                            <td>معرف المستخدم</td>
                        </tr>
                        <tr class="required">
                            <td>order_number</td>
                            <td>VARCHAR(50)</td>
                            <td>NOT NULL, UNIQUE</td>
                            <td>رقم أمر البيع</td>
                        </tr>
                        <tr class="required">
                            <td>order_date</td>
                            <td>DATE</td>
                            <td>NOT NULL</td>
                            <td>تاريخ الأمر</td>
                        </tr>
                        <tr class="optional">
                            <td>delivery_date</td>
                            <td>DATE</td>
                            <td>NULLABLE</td>
                            <td>تاريخ التسليم</td>
                        </tr>
                        <tr class="required">
                            <td>order_status</td>
                            <td>ENUM</td>
                            <td>('draft','confirmed','in_production','ready','delivered','cancelled')</td>
                            <td>حالة الأمر</td>
                        </tr>
                        <tr class="required">
                            <td>subtotal</td>
                            <td>DECIMAL(12,2)</td>
                            <td>DEFAULT 0.00</td>
                            <td>المجموع الفرعي</td>
                        </tr>
                        <tr class="required">
                            <td>discount_amount</td>
                            <td>DECIMAL(12,2)</td>
                            <td>DEFAULT 0.00</td>
                            <td>مبلغ الخصم</td>
                        </tr>
                        <tr class="required">
                            <td>tax_amount</td>
                            <td>DECIMAL(12,2)</td>
                            <td>DEFAULT 0.00</td>
                            <td>مبلغ الضريبة</td>
                        </tr>
                        <tr class="required">
                            <td>total_amount</td>
                            <td>DECIMAL(12,2)</td>
                            <td>DEFAULT 0.00</td>
                            <td>المبلغ الإجمالي</td>
                        </tr>
                        <tr class="optional">
                            <td>notes</td>
                            <td>TEXT</td>
                            <td>NULLABLE</td>
                            <td>ملاحظات</td>
                        </tr>
                        <tr class="required">
                            <td>created_at</td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP</td>
                            <td>تاريخ الإنشاء</td>
                        </tr>
                        <tr class="required">
                            <td>updated_at</td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP ON UPDATE</td>
                            <td>تاريخ التحديث</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="sql-code">
<span class="sql-comment">-- جدول أوامر البيع</span>
<span class="sql-keyword">CREATE TABLE</span> sales_orders (
    id <span class="sql-keyword">BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT</span>,
    company_id <span class="sql-keyword">BIGINT UNSIGNED NOT NULL</span>,
    branch_id <span class="sql-keyword">BIGINT UNSIGNED NOT NULL</span>,
    customer_id <span class="sql-keyword">BIGINT UNSIGNED NOT NULL</span>,
    user_id <span class="sql-keyword">BIGINT UNSIGNED NOT NULL</span>,
    order_number <span class="sql-keyword">VARCHAR</span>(50) <span class="sql-keyword">NOT NULL UNIQUE</span>,
    order_date <span class="sql-keyword">DATE NOT NULL</span>,
    delivery_date <span class="sql-keyword">DATE</span>,
    order_status <span class="sql-keyword">ENUM</span>(<span class="sql-string">'draft','confirmed','in_production','ready','delivered','cancelled'</span>) <span class="sql-keyword">DEFAULT</span> <span class="sql-string">'draft'</span>,
    subtotal <span class="sql-keyword">DECIMAL</span>(12,2) <span class="sql-keyword">DEFAULT</span> 0.00,
    discount_amount <span class="sql-keyword">DECIMAL</span>(12,2) <span class="sql-keyword">DEFAULT</span> 0.00,
    tax_amount <span class="sql-keyword">DECIMAL</span>(12,2) <span class="sql-keyword">DEFAULT</span> 0.00,
    total_amount <span class="sql-keyword">DECIMAL</span>(12,2) <span class="sql-keyword">DEFAULT</span> 0.00,
    notes <span class="sql-keyword">TEXT</span>,
    created_at <span class="sql-keyword">TIMESTAMP DEFAULT CURRENT_TIMESTAMP</span>,
    updated_at <span class="sql-keyword">TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (company_id) <span class="sql-keyword">REFERENCES</span> companies(id) <span class="sql-keyword">ON DELETE CASCADE</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (branch_id) <span class="sql-keyword">REFERENCES</span> branches(id) <span class="sql-keyword">ON DELETE CASCADE</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (customer_id) <span class="sql-keyword">REFERENCES</span> customers(id) <span class="sql-keyword">ON DELETE CASCADE</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (user_id) <span class="sql-keyword">REFERENCES</span> users(id),
    <span class="sql-keyword">INDEX</span> idx_order_number (order_number),
    <span class="sql-keyword">INDEX</span> idx_order_date (order_date),
    <span class="sql-keyword">INDEX</span> idx_order_status (order_status),
    <span class="sql-keyword">INDEX</span> idx_customer_orders (customer_id)
);
            </div>

            <h3>3.3 جدول تفاصيل أوامر البيع (sales_order_items)</h3>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>اسم الحقل</th>
                            <th>نوع البيانات</th>
                            <th>القيود</th>
                            <th>الوصف</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="primary-key">
                            <td>id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>PRIMARY KEY, AUTO_INCREMENT</td>
                            <td>المعرف الفريد للصنف</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>sales_order_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NOT NULL, FOREIGN KEY</td>
                            <td>معرف أمر البيع</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>product_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NOT NULL, FOREIGN KEY</td>
                            <td>معرف المنتج</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>product_variant_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NULLABLE, FOREIGN KEY</td>
                            <td>معرف متغير المنتج</td>
                        </tr>
                        <tr class="required">
                            <td>quantity</td>
                            <td>DECIMAL(10,3)</td>
                            <td>NOT NULL</td>
                            <td>الكمية</td>
                        </tr>
                        <tr class="required">
                            <td>unit_price</td>
                            <td>DECIMAL(12,2)</td>
                            <td>NOT NULL</td>
                            <td>سعر الوحدة</td>
                        </tr>
                        <tr class="required">
                            <td>discount_percentage</td>
                            <td>DECIMAL(5,2)</td>
                            <td>DEFAULT 0.00</td>
                            <td>نسبة الخصم</td>
                        </tr>
                        <tr class="required">
                            <td>discount_amount</td>
                            <td>DECIMAL(12,2)</td>
                            <td>DEFAULT 0.00</td>
                            <td>مبلغ الخصم</td>
                        </tr>
                        <tr class="required">
                            <td>tax_percentage</td>
                            <td>DECIMAL(5,2)</td>
                            <td>DEFAULT 0.00</td>
                            <td>نسبة الضريبة</td>
                        </tr>
                        <tr class="required">
                            <td>tax_amount</td>
                            <td>DECIMAL(12,2)</td>
                            <td>DEFAULT 0.00</td>
                            <td>مبلغ الضريبة</td>
                        </tr>
                        <tr class="required">
                            <td>line_total</td>
                            <td>DECIMAL(12,2)</td>
                            <td>NOT NULL</td>
                            <td>إجمالي السطر</td>
                        </tr>
                        <tr class="optional">
                            <td>notes</td>
                            <td>TEXT</td>
                            <td>NULLABLE</td>
                            <td>ملاحظات</td>
                        </tr>
                        <tr class="required">
                            <td>created_at</td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP</td>
                            <td>تاريخ الإنشاء</td>
                        </tr>
                        <tr class="required">
                            <td>updated_at</td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP ON UPDATE</td>
                            <td>تاريخ التحديث</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="sql-code">
<span class="sql-comment">-- جدول تفاصيل أوامر البيع</span>
<span class="sql-keyword">CREATE TABLE</span> sales_order_items (
    id <span class="sql-keyword">BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT</span>,
    sales_order_id <span class="sql-keyword">BIGINT UNSIGNED NOT NULL</span>,
    product_id <span class="sql-keyword">BIGINT UNSIGNED NOT NULL</span>,
    product_variant_id <span class="sql-keyword">BIGINT UNSIGNED</span>,
    quantity <span class="sql-keyword">DECIMAL</span>(10,3) <span class="sql-keyword">NOT NULL</span>,
    unit_price <span class="sql-keyword">DECIMAL</span>(12,2) <span class="sql-keyword">NOT NULL</span>,
    discount_percentage <span class="sql-keyword">DECIMAL</span>(5,2) <span class="sql-keyword">DEFAULT</span> 0.00,
    discount_amount <span class="sql-keyword">DECIMAL</span>(12,2) <span class="sql-keyword">DEFAULT</span> 0.00,
    tax_percentage <span class="sql-keyword">DECIMAL</span>(5,2) <span class="sql-keyword">DEFAULT</span> 0.00,
    tax_amount <span class="sql-keyword">DECIMAL</span>(12,2) <span class="sql-keyword">DEFAULT</span> 0.00,
    line_total <span class="sql-keyword">DECIMAL</span>(12,2) <span class="sql-keyword">NOT NULL</span>,
    notes <span class="sql-keyword">TEXT</span>,
    created_at <span class="sql-keyword">TIMESTAMP DEFAULT CURRENT_TIMESTAMP</span>,
    updated_at <span class="sql-keyword">TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (sales_order_id) <span class="sql-keyword">REFERENCES</span> sales_orders(id) <span class="sql-keyword">ON DELETE CASCADE</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (product_id) <span class="sql-keyword">REFERENCES</span> products(id),
    <span class="sql-keyword">FOREIGN KEY</span> (product_variant_id) <span class="sql-keyword">REFERENCES</span> product_variants(id),
    <span class="sql-keyword">INDEX</span> idx_order_items (sales_order_id),
    <span class="sql-keyword">INDEX</span> idx_product_sales (product_id)
);
            </div>
            <h3>3.4 جدول عروض الأسعار (quotations)</h3>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>اسم الحقل</th>
                            <th>نوع البيانات</th>
                            <th>القيود</th>
                            <th>الوصف</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="primary-key">
                            <td>id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>PRIMARY KEY, AUTO_INCREMENT</td>
                            <td>المعرف الفريد لعرض السعر</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>company_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NOT NULL, FOREIGN KEY</td>
                            <td>معرف الشركة</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>customer_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NOT NULL, FOREIGN KEY</td>
                            <td>معرف العميل</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>user_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NOT NULL, FOREIGN KEY</td>
                            <td>معرف المستخدم</td>
                        </tr>
                        <tr class="required">
                            <td>quotation_number</td>
                            <td>VARCHAR(50)</td>
                            <td>NOT NULL, UNIQUE</td>
                            <td>رقم عرض السعر</td>
                        </tr>
                        <tr class="required">
                            <td>quotation_date</td>
                            <td>DATE</td>
                            <td>NOT NULL</td>
                            <td>تاريخ عرض السعر</td>
                        </tr>
                        <tr class="optional">
                            <td>valid_till</td>
                            <td>DATE</td>
                            <td>NULLABLE</td>
                            <td>صالح حتى</td>
                        </tr>
                        <tr class="required">
                            <td>status</td>
                            <td>ENUM</td>
                            <td>('draft','submitted','ordered','expired','cancelled')</td>
                            <td>حالة عرض السعر</td>
                        </tr>
                        <tr class="required">
                            <td>subtotal</td>
                            <td>DECIMAL(12,2)</td>
                            <td>DEFAULT 0.00</td>
                            <td>المجموع الفرعي</td>
                        </tr>
                        <tr class="required">
                            <td>discount_amount</td>
                            <td>DECIMAL(12,2)</td>
                            <td>DEFAULT 0.00</td>
                            <td>مبلغ الخصم</td>
                        </tr>
                        <tr class="required">
                            <td>tax_amount</td>
                            <td>DECIMAL(12,2)</td>
                            <td>DEFAULT 0.00</td>
                            <td>مبلغ الضريبة</td>
                        </tr>
                        <tr class="required">
                            <td>total_amount</td>
                            <td>DECIMAL(12,2)</td>
                            <td>DEFAULT 0.00</td>
                            <td>المبلغ الإجمالي</td>
                        </tr>
                        <tr class="optional">
                            <td>terms_and_conditions</td>
                            <td>TEXT</td>
                            <td>NULLABLE</td>
                            <td>الشروط والأحكام</td>
                        </tr>
                        <tr class="optional">
                            <td>notes</td>
                            <td>TEXT</td>
                            <td>NULLABLE</td>
                            <td>ملاحظات</td>
                        </tr>
                        <tr class="required">
                            <td>created_at</td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP</td>
                            <td>تاريخ الإنشاء</td>
                        </tr>
                        <tr class="required">
                            <td>updated_at</td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP ON UPDATE</td>
                            <td>تاريخ التحديث</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="sql-code">
<span class="sql-comment">-- جدول عروض الأسعار</span>
<span class="sql-keyword">CREATE TABLE</span> quotations (
    id <span class="sql-keyword">BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT</span>,
    company_id <span class="sql-keyword">BIGINT UNSIGNED NOT NULL</span>,
    customer_id <span class="sql-keyword">BIGINT UNSIGNED NOT NULL</span>,
    user_id <span class="sql-keyword">BIGINT UNSIGNED NOT NULL</span>,
    quotation_number <span class="sql-keyword">VARCHAR</span>(50) <span class="sql-keyword">NOT NULL UNIQUE</span>,
    quotation_date <span class="sql-keyword">DATE NOT NULL</span>,
    valid_till <span class="sql-keyword">DATE</span>,
    status <span class="sql-keyword">ENUM</span>(<span class="sql-string">'draft','submitted','ordered','expired','cancelled'</span>) <span class="sql-keyword">DEFAULT</span> <span class="sql-string">'draft'</span>,
    subtotal <span class="sql-keyword">DECIMAL</span>(12,2) <span class="sql-keyword">DEFAULT</span> 0.00,
    discount_amount <span class="sql-keyword">DECIMAL</span>(12,2) <span class="sql-keyword">DEFAULT</span> 0.00,
    tax_amount <span class="sql-keyword">DECIMAL</span>(12,2) <span class="sql-keyword">DEFAULT</span> 0.00,
    total_amount <span class="sql-keyword">DECIMAL</span>(12,2) <span class="sql-keyword">DEFAULT</span> 0.00,
    terms_and_conditions <span class="sql-keyword">TEXT</span>,
    notes <span class="sql-keyword">TEXT</span>,
    created_at <span class="sql-keyword">TIMESTAMP DEFAULT CURRENT_TIMESTAMP</span>,
    updated_at <span class="sql-keyword">TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (company_id) <span class="sql-keyword">REFERENCES</span> companies(id) <span class="sql-keyword">ON DELETE CASCADE</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (customer_id) <span class="sql-keyword">REFERENCES</span> customers(id) <span class="sql-keyword">ON DELETE CASCADE</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (user_id) <span class="sql-keyword">REFERENCES</span> users(id),
    <span class="sql-keyword">INDEX</span> idx_quotation_number (quotation_number),
    <span class="sql-keyword">INDEX</span> idx_quotation_date (quotation_date),
    <span class="sql-keyword">INDEX</span> idx_quotation_status (status),
    <span class="sql-keyword">INDEX</span> idx_customer_quotations (customer_id)
);
            </div>

            <h3>3.5 جدول تفاصيل عروض الأسعار (quotation_items)</h3>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>اسم الحقل</th>
                            <th>نوع البيانات</th>
                            <th>القيود</th>
                            <th>الوصف</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="primary-key">
                            <td>id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>PRIMARY KEY, AUTO_INCREMENT</td>
                            <td>المعرف الفريد للصنف</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>quotation_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NOT NULL, FOREIGN KEY</td>
                            <td>معرف عرض السعر</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>product_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NOT NULL, FOREIGN KEY</td>
                            <td>معرف المنتج</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>product_variant_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NULLABLE, FOREIGN KEY</td>
                            <td>معرف متغير المنتج</td>
                        </tr>
                        <tr class="required">
                            <td>quantity</td>
                            <td>DECIMAL(10,3)</td>
                            <td>NOT NULL</td>
                            <td>الكمية</td>
                        </tr>
                        <tr class="required">
                            <td>unit_price</td>
                            <td>DECIMAL(12,2)</td>
                            <td>NOT NULL</td>
                            <td>سعر الوحدة</td>
                        </tr>
                        <tr class="required">
                            <td>discount_percentage</td>
                            <td>DECIMAL(5,2)</td>
                            <td>DEFAULT 0.00</td>
                            <td>نسبة الخصم</td>
                        </tr>
                        <tr class="required">
                            <td>discount_amount</td>
                            <td>DECIMAL(12,2)</td>
                            <td>DEFAULT 0.00</td>
                            <td>مبلغ الخصم</td>
                        </tr>
                        <tr class="required">
                            <td>tax_percentage</td>
                            <td>DECIMAL(5,2)</td>
                            <td>DEFAULT 0.00</td>
                            <td>نسبة الضريبة</td>
                        </tr>
                        <tr class="required">
                            <td>tax_amount</td>
                            <td>DECIMAL(12,2)</td>
                            <td>DEFAULT 0.00</td>
                            <td>مبلغ الضريبة</td>
                        </tr>
                        <tr class="required">
                            <td>line_total</td>
                            <td>DECIMAL(12,2)</td>
                            <td>NOT NULL</td>
                            <td>إجمالي السطر</td>
                        </tr>
                        <tr class="optional">
                            <td>notes</td>
                            <td>TEXT</td>
                            <td>NULLABLE</td>
                            <td>ملاحظات</td>
                        </tr>
                        <tr class="required">
                            <td>created_at</td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP</td>
                            <td>تاريخ الإنشاء</td>
                        </tr>
                        <tr class="required">
                            <td>updated_at</td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP ON UPDATE</td>
                            <td>تاريخ التحديث</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="sql-code">
<span class="sql-comment">-- جدول تفاصيل عروض الأسعار</span>
<span class="sql-keyword">CREATE TABLE</span> quotation_items (
    id <span class="sql-keyword">BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT</span>,
    quotation_id <span class="sql-keyword">BIGINT UNSIGNED NOT NULL</span>,
    product_id <span class="sql-keyword">BIGINT UNSIGNED NOT NULL</span>,
    product_variant_id <span class="sql-keyword">BIGINT UNSIGNED</span>,
    quantity <span class="sql-keyword">DECIMAL</span>(10,3) <span class="sql-keyword">NOT NULL</span>,
    unit_price <span class="sql-keyword">DECIMAL</span>(12,2) <span class="sql-keyword">NOT NULL</span>,
    discount_percentage <span class="sql-keyword">DECIMAL</span>(5,2) <span class="sql-keyword">DEFAULT</span> 0.00,
    discount_amount <span class="sql-keyword">DECIMAL</span>(12,2) <span class="sql-keyword">DEFAULT</span> 0.00,
    tax_percentage <span class="sql-keyword">DECIMAL</span>(5,2) <span class="sql-keyword">DEFAULT</span> 0.00,
    tax_amount <span class="sql-keyword">DECIMAL</span>(12,2) <span class="sql-keyword">DEFAULT</span> 0.00,
    line_total <span class="sql-keyword">DECIMAL</span>(12,2) <span class="sql-keyword">NOT NULL</span>,
    notes <span class="sql-keyword">TEXT</span>,
    created_at <span class="sql-keyword">TIMESTAMP DEFAULT CURRENT_TIMESTAMP</span>,
    updated_at <span class="sql-keyword">TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (quotation_id) <span class="sql-keyword">REFERENCES</span> quotations(id) <span class="sql-keyword">ON DELETE CASCADE</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (product_id) <span class="sql-keyword">REFERENCES</span> products(id),
    <span class="sql-keyword">FOREIGN KEY</span> (product_variant_id) <span class="sql-keyword">REFERENCES</span> product_variants(id),
    <span class="sql-keyword">INDEX</span> idx_quotation_items (quotation_id),
    <span class="sql-keyword">INDEX</span> idx_product_quotations (product_id)
);
            </div>

            <h3>3.8 جدول إشعارات التسليم (delivery_notes)</h3>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>اسم الحقل</th>
                            <th>نوع البيانات</th>
                            <th>القيود</th>
                            <th>الوصف</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="primary-key">
                            <td>id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>PRIMARY KEY, AUTO_INCREMENT</td>
                            <td>المعرف الفريد لإشعار التسليم</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>company_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NOT NULL, FOREIGN KEY</td>
                            <td>معرف الشركة</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>sales_order_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NOT NULL, FOREIGN KEY</td>
                            <td>معرف أمر البيع</td>
                        </tr>
                        <tr class="required">
                            <td>delivery_note_number</td>
                            <td>VARCHAR(50)</td>
                            <td>NOT NULL</td>
                            <td>رقم إشعار التسليم</td>
                        </tr>
                        <tr class="required">
                            <td>delivery_date</td>
                            <td>DATE</td>
                            <td>NOT NULL</td>
                            <td>تاريخ التسليم</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>customer_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NOT NULL, FOREIGN KEY</td>
                            <td>معرف العميل</td>
                        </tr>
                        <tr class="optional">
                            <td>shipping_address</td>
                            <td>TEXT</td>
                            <td>NULLABLE</td>
                            <td>عنوان الشحن</td>
                        </tr>
                        <tr class="required">
                            <td>total_qty</td>
                            <td>DECIMAL(10,3)</td>
                            <td>DEFAULT 0.000</td>
                            <td>إجمالي الكمية</td>
                        </tr>
                        <tr class="required">
                            <td>status</td>
                            <td>ENUM</td>
                            <td>('draft','submitted','delivered','cancelled')</td>
                            <td>حالة إشعار التسليم</td>
                        </tr>
                        <tr class="optional">
                            <td>notes</td>
                            <td>TEXT</td>
                            <td>NULLABLE</td>
                            <td>ملاحظات</td>
                        </tr>
                        <tr class="required">
                            <td>created_at</td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP</td>
                            <td>تاريخ الإنشاء</td>
                        </tr>
                        <tr class="required">
                            <td>updated_at</td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP ON UPDATE</td>
                            <td>تاريخ التحديث</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="sql-code">
<span class="sql-comment">-- جدول إشعارات التسليم</span>
<span class="sql-keyword">CREATE TABLE</span> delivery_notes (
    id <span class="sql-keyword">BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT</span>,
    company_id <span class="sql-keyword">BIGINT UNSIGNED NOT NULL</span>,
    sales_order_id <span class="sql-keyword">BIGINT UNSIGNED NOT NULL</span>,
    delivery_note_number <span class="sql-keyword">VARCHAR</span>(50) <span class="sql-keyword">NOT NULL</span>,
    delivery_date <span class="sql-keyword">DATE NOT NULL</span>,
    customer_id <span class="sql-keyword">BIGINT UNSIGNED NOT NULL</span>,
    shipping_address <span class="sql-keyword">TEXT</span>,
    total_qty <span class="sql-keyword">DECIMAL</span>(10,3) <span class="sql-keyword">DEFAULT</span> 0.000,
    status <span class="sql-keyword">ENUM</span>(<span class="sql-string">'draft','submitted','delivered','cancelled'</span>) <span class="sql-keyword">DEFAULT</span> <span class="sql-string">'draft'</span>,
    notes <span class="sql-keyword">TEXT</span>,
    created_at <span class="sql-keyword">TIMESTAMP DEFAULT CURRENT_TIMESTAMP</span>,
    updated_at <span class="sql-keyword">TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (company_id) <span class="sql-keyword">REFERENCES</span> companies(id) <span class="sql-keyword">ON DELETE CASCADE</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (sales_order_id) <span class="sql-keyword">REFERENCES</span> sales_orders(id) <span class="sql-keyword">ON DELETE CASCADE</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (customer_id) <span class="sql-keyword">REFERENCES</span> customers(id),
    <span class="sql-keyword">UNIQUE KEY</span> unique_delivery_note_number (company_id, delivery_note_number),
    <span class="sql-keyword">INDEX</span> idx_delivery_date (delivery_date),
    <span class="sql-keyword">INDEX</span> idx_delivery_status (status)
);
            </div>

            <h3>3.9 جدول تفاصيل إشعارات التسليم (delivery_note_items)</h3>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>اسم الحقل</th>
                            <th>نوع البيانات</th>
                            <th>القيود</th>
                            <th>الوصف</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="primary-key">
                            <td>id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>PRIMARY KEY, AUTO_INCREMENT</td>
                            <td>المعرف الفريد للتفصيل</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>delivery_note_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NOT NULL, FOREIGN KEY</td>
                            <td>معرف إشعار التسليم</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>product_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NOT NULL, FOREIGN KEY</td>
                            <td>معرف المنتج</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>product_variant_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NULLABLE, FOREIGN KEY</td>
                            <td>معرف متغير المنتج</td>
                        </tr>
                        <tr class="required">
                            <td>qty</td>
                            <td>DECIMAL(10,3)</td>
                            <td>NOT NULL</td>
                            <td>الكمية المسلمة</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>warehouse_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NOT NULL, FOREIGN KEY</td>
                            <td>معرف المستودع</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>batch_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NULLABLE, FOREIGN KEY</td>
                            <td>معرف الدفعة</td>
                        </tr>
                        <tr class="optional">
                            <td>serial_numbers</td>
                            <td>JSON</td>
                            <td>NULLABLE</td>
                            <td>الأرقام التسلسلية</td>
                        </tr>
                        <tr class="required">
                            <td>created_at</td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP</td>
                            <td>تاريخ الإنشاء</td>
                        </tr>
                        <tr class="required">
                            <td>updated_at</td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP ON UPDATE</td>
                            <td>تاريخ التحديث</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="sql-code">
<span class="sql-comment">-- جدول تفاصيل إشعارات التسليم</span>
<span class="sql-keyword">CREATE TABLE</span> delivery_note_items (
    id <span class="sql-keyword">BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT</span>,
    delivery_note_id <span class="sql-keyword">BIGINT UNSIGNED NOT NULL</span>,
    product_id <span class="sql-keyword">BIGINT UNSIGNED NOT NULL</span>,
    product_variant_id <span class="sql-keyword">BIGINT UNSIGNED</span>,
    qty <span class="sql-keyword">DECIMAL</span>(10,3) <span class="sql-keyword">NOT NULL</span>,
    warehouse_id <span class="sql-keyword">BIGINT UNSIGNED NOT NULL</span>,
    batch_id <span class="sql-keyword">BIGINT UNSIGNED</span>,
    serial_numbers <span class="sql-keyword">JSON</span>,
    created_at <span class="sql-keyword">TIMESTAMP DEFAULT CURRENT_TIMESTAMP</span>,
    updated_at <span class="sql-keyword">TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (delivery_note_id) <span class="sql-keyword">REFERENCES</span> delivery_notes(id) <span class="sql-keyword">ON DELETE CASCADE</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (product_id) <span class="sql-keyword">REFERENCES</span> products(id),
    <span class="sql-keyword">FOREIGN KEY</span> (product_variant_id) <span class="sql-keyword">REFERENCES</span> product_variants(id),
    <span class="sql-keyword">FOREIGN KEY</span> (warehouse_id) <span class="sql-keyword">REFERENCES</span> warehouses(id),
    <span class="sql-keyword">FOREIGN KEY</span> (batch_id) <span class="sql-keyword">REFERENCES</span> batches(id),
    <span class="sql-keyword">INDEX</span> idx_delivery_product (product_id),
    <span class="sql-keyword">INDEX</span> idx_delivery_warehouse (warehouse_id)
);
            </div>
        </div>
    </div>
</body>
</html>
