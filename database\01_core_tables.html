<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الجداول الأساسية - نظام ERP للخياطة</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Cairo', sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background: white;
            margin-top: 20px;
            margin-bottom: 20px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            padding: 40px 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 2.5em;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .section {
            margin-bottom: 50px;
            padding: 30px;
            background: #f8f9fa;
            border-radius: 15px;
            border: 1px solid #e9ecef;
        }
        
        .section h2 {
            color: #495057;
            font-size: 2em;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 3px solid #667eea;
        }
        
        .section h3 {
            color: #6c757d;
            font-size: 1.5em;
            margin: 25px 0 15px 0;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border-left: 4px solid #28a745;
        }
        
        .table-container {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            margin: 20px 0;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
        }
        
        th, td {
            padding: 12px 15px;
            text-align: right;
            border-bottom: 1px solid #dee2e6;
            font-size: 0.9em;
        }
        
        th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: 600;
            position: sticky;
            top: 0;
        }
        
        tr:hover {
            background: #f8f9fa;
        }
        
        .primary-key {
            background: #fff3cd;
            font-weight: bold;
        }
        
        .foreign-key {
            background: #d1ecf1;
        }
        
        .required {
            color: #dc3545;
            font-weight: bold;
        }
        
        .optional {
            color: #6c757d;
        }
        
        .sql-code {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.85em;
            overflow-x: auto;
            margin: 20px 0;
            white-space: pre-wrap;
        }
        
        .sql-keyword {
            color: #63b3ed;
            font-weight: bold;
        }
        
        .sql-string {
            color: #68d391;
        }
        
        .sql-comment {
            color: #a0aec0;
            font-style: italic;
        }
        
        .info-box {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-left: 5px solid #17a2b8;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-left: 5px solid #fdcb6e;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .nav-links {
            background: #e9ecef;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .nav-links a {
            display: inline-block;
            margin: 5px 10px;
            padding: 10px 20px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-weight: 500;
        }
        
        .nav-links a:hover {
            background: #5a67d8;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>الجداول الأساسية</h1>
            <p>نظام ERP لمعامل ومحلات الخياطة</p>
        </div>

        <div class="nav-links">
            <a href="../index.html">الصفحة الرئيسية</a>
            <a href="01_core_tables.html">الجداول الأساسية</a>
            <a href="02_product_inventory_tables.html">المنتجات والمخزون</a>
            <a href="03_sales_pos_tables.html">المبيعات ونقطة البيع</a>
            <a href="04_accounting_tables.html">المحاسبة</a>
            <a href="05_manufacturing_tables.html">التصنيع</a>
            <a href="06_crm_tables.html">إدارة العملاء</a>
            <a href="07_purchasing_tables.html">المشتريات والموردين</a>
            <a href="08_system_tables.html">جداول النظام</a>
        </div>

        <div class="section">
            <h2>1. الجداول الأساسية للنظام</h2>
            
            <div class="info-box">
                <h4>نظرة عامة:</h4>
                <p>هذه الجداول تشكل الأساس لجميع عمليات النظام وتحتوي على البيانات الأساسية مثل الشركات، الفروع، المستخدمين، والإعدادات العامة.</p>
            </div>

            <h3>1.1 جدول الشركات (companies)</h3>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>اسم الحقل</th>
                            <th>نوع البيانات</th>
                            <th>القيود</th>
                            <th>الوصف</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="primary-key">
                            <td>id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>PRIMARY KEY, AUTO_INCREMENT</td>
                            <td>المعرف الفريد للشركة</td>
                        </tr>
                        <tr class="required">
                            <td>name</td>
                            <td>VARCHAR(255)</td>
                            <td>NOT NULL</td>
                            <td>اسم الشركة</td>
                        </tr>
                        <tr class="required">
                            <td>code</td>
                            <td>VARCHAR(50)</td>
                            <td>NOT NULL, UNIQUE</td>
                            <td>رمز الشركة</td>
                        </tr>
                        <tr class="optional">
                            <td>legal_name</td>
                            <td>VARCHAR(255)</td>
                            <td>NULLABLE</td>
                            <td>الاسم القانوني</td>
                        </tr>
                        <tr class="optional">
                            <td>tax_number</td>
                            <td>VARCHAR(50)</td>
                            <td>NULLABLE</td>
                            <td>الرقم الضريبي</td>
                        </tr>
                        <tr class="optional">
                            <td>commercial_register</td>
                            <td>VARCHAR(50)</td>
                            <td>NULLABLE</td>
                            <td>السجل التجاري</td>
                        </tr>
                        <tr class="optional">
                            <td>address</td>
                            <td>TEXT</td>
                            <td>NULLABLE</td>
                            <td>العنوان</td>
                        </tr>
                        <tr class="optional">
                            <td>city</td>
                            <td>VARCHAR(100)</td>
                            <td>NULLABLE</td>
                            <td>المدينة</td>
                        </tr>
                        <tr class="optional">
                            <td>country</td>
                            <td>VARCHAR(100)</td>
                            <td>NULLABLE</td>
                            <td>البلد</td>
                        </tr>
                        <tr class="optional">
                            <td>phone</td>
                            <td>VARCHAR(20)</td>
                            <td>NULLABLE</td>
                            <td>رقم الهاتف</td>
                        </tr>
                        <tr class="optional">
                            <td>email</td>
                            <td>VARCHAR(255)</td>
                            <td>NULLABLE</td>
                            <td>البريد الإلكتروني</td>
                        </tr>
                        <tr class="optional">
                            <td>website</td>
                            <td>VARCHAR(255)</td>
                            <td>NULLABLE</td>
                            <td>الموقع الإلكتروني</td>
                        </tr>
                        <tr class="optional">
                            <td>logo</td>
                            <td>VARCHAR(255)</td>
                            <td>NULLABLE</td>
                            <td>مسار الشعار</td>
                        </tr>
                        <tr class="required">
                            <td>currency</td>
                            <td>VARCHAR(3)</td>
                            <td>DEFAULT 'SAR'</td>
                            <td>العملة الأساسية</td>
                        </tr>
                        <tr class="required">
                            <td>fiscal_year_start</td>
                            <td>DATE</td>
                            <td>NOT NULL</td>
                            <td>بداية السنة المالية</td>
                        </tr>
                        <tr class="required">
                            <td>fiscal_year_end</td>
                            <td>DATE</td>
                            <td>NOT NULL</td>
                            <td>نهاية السنة المالية</td>
                        </tr>
                        <tr class="required">
                            <td>is_active</td>
                            <td>BOOLEAN</td>
                            <td>DEFAULT TRUE</td>
                            <td>حالة النشاط</td>
                        </tr>
                        <tr class="required">
                            <td>created_at</td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP</td>
                            <td>تاريخ الإنشاء</td>
                        </tr>
                        <tr class="required">
                            <td>updated_at</td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP ON UPDATE</td>
                            <td>تاريخ التحديث</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="sql-code">
<span class="sql-comment">-- جدول الشركات</span>
<span class="sql-keyword">CREATE TABLE</span> companies (
    id <span class="sql-keyword">BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT</span>,
    name <span class="sql-keyword">VARCHAR</span>(255) <span class="sql-keyword">NOT NULL</span>,
    code <span class="sql-keyword">VARCHAR</span>(50) <span class="sql-keyword">NOT NULL UNIQUE</span>,
    legal_name <span class="sql-keyword">VARCHAR</span>(255),
    tax_number <span class="sql-keyword">VARCHAR</span>(50),
    commercial_register <span class="sql-keyword">VARCHAR</span>(50),
    address <span class="sql-keyword">TEXT</span>,
    city <span class="sql-keyword">VARCHAR</span>(100),
    country <span class="sql-keyword">VARCHAR</span>(100),
    phone <span class="sql-keyword">VARCHAR</span>(20),
    email <span class="sql-keyword">VARCHAR</span>(255),
    website <span class="sql-keyword">VARCHAR</span>(255),
    logo <span class="sql-keyword">VARCHAR</span>(255),
    currency <span class="sql-keyword">VARCHAR</span>(3) <span class="sql-keyword">DEFAULT</span> <span class="sql-string">'SAR'</span>,
    fiscal_year_start <span class="sql-keyword">DATE NOT NULL</span>,
    fiscal_year_end <span class="sql-keyword">DATE NOT NULL</span>,
    is_active <span class="sql-keyword">BOOLEAN DEFAULT TRUE</span>,
    created_at <span class="sql-keyword">TIMESTAMP DEFAULT CURRENT_TIMESTAMP</span>,
    updated_at <span class="sql-keyword">TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP</span>
);
            </div>
            <h3>1.2 جدول الفروع (branches)</h3>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>اسم الحقل</th>
                            <th>نوع البيانات</th>
                            <th>القيود</th>
                            <th>الوصف</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="primary-key">
                            <td>id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>PRIMARY KEY, AUTO_INCREMENT</td>
                            <td>المعرف الفريد للفرع</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>company_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NOT NULL, FOREIGN KEY</td>
                            <td>معرف الشركة</td>
                        </tr>
                        <tr class="required">
                            <td>name</td>
                            <td>VARCHAR(255)</td>
                            <td>NOT NULL</td>
                            <td>اسم الفرع</td>
                        </tr>
                        <tr class="required">
                            <td>code</td>
                            <td>VARCHAR(50)</td>
                            <td>NOT NULL</td>
                            <td>رمز الفرع</td>
                        </tr>
                        <tr class="optional">
                            <td>address</td>
                            <td>TEXT</td>
                            <td>NULLABLE</td>
                            <td>عنوان الفرع</td>
                        </tr>
                        <tr class="optional">
                            <td>city</td>
                            <td>VARCHAR(100)</td>
                            <td>NULLABLE</td>
                            <td>المدينة</td>
                        </tr>
                        <tr class="optional">
                            <td>phone</td>
                            <td>VARCHAR(20)</td>
                            <td>NULLABLE</td>
                            <td>رقم الهاتف</td>
                        </tr>
                        <tr class="optional">
                            <td>email</td>
                            <td>VARCHAR(255)</td>
                            <td>NULLABLE</td>
                            <td>البريد الإلكتروني</td>
                        </tr>
                        <tr class="optional">
                            <td>manager_name</td>
                            <td>VARCHAR(255)</td>
                            <td>NULLABLE</td>
                            <td>اسم مدير الفرع</td>
                        </tr>
                        <tr class="required">
                            <td>branch_type</td>
                            <td>ENUM</td>
                            <td>('main','branch','warehouse','showroom')</td>
                            <td>نوع الفرع</td>
                        </tr>
                        <tr class="required">
                            <td>is_active</td>
                            <td>BOOLEAN</td>
                            <td>DEFAULT TRUE</td>
                            <td>حالة النشاط</td>
                        </tr>
                        <tr class="required">
                            <td>created_at</td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP</td>
                            <td>تاريخ الإنشاء</td>
                        </tr>
                        <tr class="required">
                            <td>updated_at</td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP ON UPDATE</td>
                            <td>تاريخ التحديث</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="sql-code">
<span class="sql-comment">-- جدول الفروع</span>
<span class="sql-keyword">CREATE TABLE</span> branches (
    id <span class="sql-keyword">BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT</span>,
    company_id <span class="sql-keyword">BIGINT UNSIGNED NOT NULL</span>,
    name <span class="sql-keyword">VARCHAR</span>(255) <span class="sql-keyword">NOT NULL</span>,
    code <span class="sql-keyword">VARCHAR</span>(50) <span class="sql-keyword">NOT NULL</span>,
    address <span class="sql-keyword">TEXT</span>,
    city <span class="sql-keyword">VARCHAR</span>(100),
    phone <span class="sql-keyword">VARCHAR</span>(20),
    email <span class="sql-keyword">VARCHAR</span>(255),
    manager_name <span class="sql-keyword">VARCHAR</span>(255),
    branch_type <span class="sql-keyword">ENUM</span>(<span class="sql-string">'main','branch','warehouse','showroom'</span>) <span class="sql-keyword">DEFAULT</span> <span class="sql-string">'branch'</span>,
    is_active <span class="sql-keyword">BOOLEAN DEFAULT TRUE</span>,
    created_at <span class="sql-keyword">TIMESTAMP DEFAULT CURRENT_TIMESTAMP</span>,
    updated_at <span class="sql-keyword">TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (company_id) <span class="sql-keyword">REFERENCES</span> companies(id) <span class="sql-keyword">ON DELETE CASCADE</span>,
    <span class="sql-keyword">UNIQUE KEY</span> unique_branch_code (company_id, code)
);
            </div>

            <h3>1.3 جدول المستخدمين (users)</h3>
            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>اسم الحقل</th>
                            <th>نوع البيانات</th>
                            <th>القيود</th>
                            <th>الوصف</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr class="primary-key">
                            <td>id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>PRIMARY KEY, AUTO_INCREMENT</td>
                            <td>المعرف الفريد للمستخدم</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>company_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NOT NULL, FOREIGN KEY</td>
                            <td>معرف الشركة</td>
                        </tr>
                        <tr class="foreign-key">
                            <td>branch_id</td>
                            <td>BIGINT UNSIGNED</td>
                            <td>NULLABLE, FOREIGN KEY</td>
                            <td>معرف الفرع</td>
                        </tr>
                        <tr class="required">
                            <td>name</td>
                            <td>VARCHAR(255)</td>
                            <td>NOT NULL</td>
                            <td>اسم المستخدم</td>
                        </tr>
                        <tr class="required">
                            <td>email</td>
                            <td>VARCHAR(255)</td>
                            <td>NOT NULL, UNIQUE</td>
                            <td>البريد الإلكتروني</td>
                        </tr>
                        <tr class="required">
                            <td>username</td>
                            <td>VARCHAR(100)</td>
                            <td>NOT NULL, UNIQUE</td>
                            <td>اسم المستخدم للدخول</td>
                        </tr>
                        <tr class="required">
                            <td>password</td>
                            <td>VARCHAR(255)</td>
                            <td>NOT NULL</td>
                            <td>كلمة المرور المشفرة</td>
                        </tr>
                        <tr class="optional">
                            <td>phone</td>
                            <td>VARCHAR(20)</td>
                            <td>NULLABLE</td>
                            <td>رقم الهاتف</td>
                        </tr>
                        <tr class="optional">
                            <td>employee_id</td>
                            <td>VARCHAR(50)</td>
                            <td>NULLABLE</td>
                            <td>رقم الموظف</td>
                        </tr>
                        <tr class="optional">
                            <td>avatar</td>
                            <td>VARCHAR(255)</td>
                            <td>NULLABLE</td>
                            <td>صورة المستخدم</td>
                        </tr>
                        <tr class="required">
                            <td>user_type</td>
                            <td>ENUM</td>
                            <td>('super_admin','admin','manager','employee')</td>
                            <td>نوع المستخدم</td>
                        </tr>
                        <tr class="optional">
                            <td>email_verified_at</td>
                            <td>TIMESTAMP</td>
                            <td>NULLABLE</td>
                            <td>تاريخ تأكيد البريد</td>
                        </tr>
                        <tr class="optional">
                            <td>last_login_at</td>
                            <td>TIMESTAMP</td>
                            <td>NULLABLE</td>
                            <td>آخر تسجيل دخول</td>
                        </tr>
                        <tr class="optional">
                            <td>last_login_ip</td>
                            <td>VARCHAR(45)</td>
                            <td>NULLABLE</td>
                            <td>آخر IP للدخول</td>
                        </tr>
                        <tr class="required">
                            <td>is_active</td>
                            <td>BOOLEAN</td>
                            <td>DEFAULT TRUE</td>
                            <td>حالة النشاط</td>
                        </tr>
                        <tr class="optional">
                            <td>two_factor_enabled</td>
                            <td>BOOLEAN</td>
                            <td>DEFAULT FALSE</td>
                            <td>تفعيل المصادقة الثنائية</td>
                        </tr>
                        <tr class="required">
                            <td>created_at</td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP</td>
                            <td>تاريخ الإنشاء</td>
                        </tr>
                        <tr class="required">
                            <td>updated_at</td>
                            <td>TIMESTAMP</td>
                            <td>DEFAULT CURRENT_TIMESTAMP ON UPDATE</td>
                            <td>تاريخ التحديث</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="sql-code">
<span class="sql-comment">-- جدول المستخدمين</span>
<span class="sql-keyword">CREATE TABLE</span> users (
    id <span class="sql-keyword">BIGINT UNSIGNED PRIMARY KEY AUTO_INCREMENT</span>,
    company_id <span class="sql-keyword">BIGINT UNSIGNED NOT NULL</span>,
    branch_id <span class="sql-keyword">BIGINT UNSIGNED</span>,
    name <span class="sql-keyword">VARCHAR</span>(255) <span class="sql-keyword">NOT NULL</span>,
    email <span class="sql-keyword">VARCHAR</span>(255) <span class="sql-keyword">NOT NULL UNIQUE</span>,
    username <span class="sql-keyword">VARCHAR</span>(100) <span class="sql-keyword">NOT NULL UNIQUE</span>,
    password <span class="sql-keyword">VARCHAR</span>(255) <span class="sql-keyword">NOT NULL</span>,
    phone <span class="sql-keyword">VARCHAR</span>(20),
    employee_id <span class="sql-keyword">VARCHAR</span>(50),
    avatar <span class="sql-keyword">VARCHAR</span>(255),
    user_type <span class="sql-keyword">ENUM</span>(<span class="sql-string">'super_admin','admin','manager','employee'</span>) <span class="sql-keyword">DEFAULT</span> <span class="sql-string">'employee'</span>,
    email_verified_at <span class="sql-keyword">TIMESTAMP</span>,
    last_login_at <span class="sql-keyword">TIMESTAMP</span>,
    last_login_ip <span class="sql-keyword">VARCHAR</span>(45),
    is_active <span class="sql-keyword">BOOLEAN DEFAULT TRUE</span>,
    two_factor_enabled <span class="sql-keyword">BOOLEAN DEFAULT FALSE</span>,
    created_at <span class="sql-keyword">TIMESTAMP DEFAULT CURRENT_TIMESTAMP</span>,
    updated_at <span class="sql-keyword">TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (company_id) <span class="sql-keyword">REFERENCES</span> companies(id) <span class="sql-keyword">ON DELETE CASCADE</span>,
    <span class="sql-keyword">FOREIGN KEY</span> (branch_id) <span class="sql-keyword">REFERENCES</span> branches(id) <span class="sql-keyword">ON DELETE SET NULL</span>
);
            </div>
        </div>
    </div>
</body>
</html>
